// Relief timetable management module
class ReliefManager {
    constructor() {
        this.periodTimes = Utils.getPeriodTimes();
        this.daysOfWeek = Utils.getDaysOfWeek();
    }

    async createReliefTimetable(date, absentTeacherIds) {
        try {
            if (!date || !Array.isArray(absentTeacherIds) || absentTeacherIds.length === 0) {
                return { success: false, message: 'Date and absent teachers are required' };
            }

            // Get all teachers
            const allTeachers = await timetableManager.getAllTeachers();
            const absentTeachers = allTeachers.filter(t => absentTeacherIds.includes(t.id));
            const availableTeachers = allTeachers.filter(t => 
                !absentTeacherIds.includes(t.id) && t.isActive
            );

            if (availableTeachers.length === 0) {
                return { success: false, message: 'No available teachers for relief assignments' };
            }

            // Generate relief assignments
            const reliefAssignments = this.generateReliefAssignments(absentTeachers, availableTeachers);
            
            // Check for existing relief timetable for this date
            const existingRelief = await this.getReliefTimetableByDate(date);
            
            const reliefData = {
                date: date,
                absentTeachers: absentTeachers.map(t => ({
                    id: t.id,
                    name: t.name,
                    subject: t.subject
                })),
                availableTeachers: availableTeachers.map(t => ({
                    id: t.id,
                    name: t.name,
                    subject: t.subject
                })),
                reliefAssignments: reliefAssignments,
                createdBy: auth.getCurrentUser()?.username || 'system'
            };

            let reliefId;
            if (existingRelief) {
                reliefData.id = existingRelief.id;
                await db.update('reliefTimetables', reliefData);
                reliefId = existingRelief.id;
            } else {
                reliefId = await db.add('reliefTimetables', reliefData);
            }

            return { 
                success: true, 
                reliefId, 
                reliefData,
                message: existingRelief ? 'Relief timetable updated successfully' : 'Relief timetable created successfully' 
            };
        } catch (error) {
            console.error('Error creating relief timetable:', error);
            return { success: false, message: 'Failed to create relief timetable' };
        }
    }

    generateReliefAssignments(absentTeachers, availableTeachers) {
        const assignments = {};
        
        // Initialize assignments structure
        this.daysOfWeek.forEach(day => {
            assignments[day] = {};
            Object.keys(this.periodTimes).forEach(period => {
                if (period !== 'interval') {
                    assignments[day][period] = [];
                }
            });
        });

        // Collect all periods that need relief coverage
        const periodsNeedingRelief = [];
        
        absentTeachers.forEach(teacher => {
            if (teacher.timetable) {
                this.daysOfWeek.forEach(day => {
                    Object.keys(this.periodTimes).forEach(period => {
                        if (period !== 'interval' && 
                            teacher.timetable[day] && 
                            teacher.timetable[day][period] && 
                            teacher.timetable[day][period].trim() !== '') {
                            
                            periodsNeedingRelief.push({
                                day: day,
                                period: period,
                                subject: teacher.timetable[day][period],
                                originalTeacher: teacher.name,
                                originalTeacherId: teacher.id
                            });
                        }
                    });
                });
            }
        });

        // Count current load for each available teacher
        const teacherLoads = {};
        availableTeachers.forEach(teacher => {
            teacherLoads[teacher.id] = this.countTeacherPeriods(teacher);
        });

        // Assign relief periods to available teachers
        periodsNeedingRelief.forEach(reliefPeriod => {
            // Find available teachers for this specific period
            const availableForPeriod = availableTeachers.filter(teacher => {
                return this.isTeacherFreeAtPeriod(teacher, reliefPeriod.day, reliefPeriod.period);
            });

            if (availableForPeriod.length > 0) {
                // Sort by current load (ascending) to distribute evenly
                availableForPeriod.sort((a, b) => teacherLoads[a.id] - teacherLoads[b.id]);
                
                const assignedTeacher = availableForPeriod[0];
                
                assignments[reliefPeriod.day][reliefPeriod.period].push({
                    reliefTeacher: assignedTeacher.name,
                    reliefTeacherId: assignedTeacher.id,
                    subject: reliefPeriod.subject,
                    originalTeacher: reliefPeriod.originalTeacher,
                    originalTeacherId: reliefPeriod.originalTeacherId,
                    type: 'relief'
                });

                // Increment the load for this teacher
                teacherLoads[assignedTeacher.id]++;
            } else {
                // No teacher available - mark as unassigned
                assignments[reliefPeriod.day][reliefPeriod.period].push({
                    reliefTeacher: 'UNASSIGNED',
                    reliefTeacherId: null,
                    subject: reliefPeriod.subject,
                    originalTeacher: reliefPeriod.originalTeacher,
                    originalTeacherId: reliefPeriod.originalTeacherId,
                    type: 'unassigned'
                });
            }
        });

        return assignments;
    }

    isTeacherFreeAtPeriod(teacher, day, period) {
        if (!teacher.timetable || !teacher.timetable[day]) {
            return true;
        }
        
        const teacherPeriod = teacher.timetable[day][period];
        return !teacherPeriod || teacherPeriod.trim() === '';
    }

    countTeacherPeriods(teacher) {
        let count = 0;
        if (teacher.timetable) {
            this.daysOfWeek.forEach(day => {
                Object.keys(this.periodTimes).forEach(period => {
                    if (period !== 'interval' && 
                        teacher.timetable[day] && 
                        teacher.timetable[day][period] && 
                        teacher.timetable[day][period].trim() !== '') {
                        count++;
                    }
                });
            });
        }
        return count;
    }

    async getReliefTimetableByDate(date) {
        try {
            const reliefTimetables = await db.getAllByIndex('reliefTimetables', 'date', date);
            return reliefTimetables.length > 0 ? reliefTimetables[0] : null;
        } catch (error) {
            console.error('Error getting relief timetable by date:', error);
            return null;
        }
    }

    async getAllReliefTimetables() {
        try {
            const reliefTimetables = await db.getAll('reliefTimetables');
            return reliefTimetables.sort((a, b) => new Date(b.date) - new Date(a.date));
        } catch (error) {
            console.error('Error getting all relief timetables:', error);
            return [];
        }
    }

    async deleteReliefTimetable(reliefId) {
        try {
            await db.delete('reliefTimetables', reliefId);
            return { success: true, message: 'Relief timetable deleted successfully' };
        } catch (error) {
            console.error('Error deleting relief timetable:', error);
            return { success: false, message: 'Failed to delete relief timetable' };
        }
    }

    renderReliefTimetable(reliefData) {
        const container = document.createElement('div');
        container.className = 'relief-timetable-container';

        // Header with date and summary
        const header = document.createElement('div');
        header.className = 'relief-header';
        header.innerHTML = `
            <h3>Relief Timetable for ${Utils.formatDate(reliefData.date)}</h3>
            <div class="relief-summary">
                <p><strong>Absent Teachers:</strong> ${reliefData.absentTeachers.map(t => t.name).join(', ')}</p>
                <p><strong>Available Teachers:</strong> ${reliefData.availableTeachers.length}</p>
            </div>
        `;
        container.appendChild(header);

        // Relief timetable table
        const table = document.createElement('table');
        table.className = 'timetable-table relief-table';

        // Create header row
        const headerRow = document.createElement('tr');
        headerRow.innerHTML = '<th>Time</th>';
        this.daysOfWeek.forEach(day => {
            headerRow.innerHTML += `<th>${day}</th>`;
        });
        table.appendChild(headerRow);

        // Create period rows in correct order (1-5, interval, 6-8)
        const periodOrder = ['1', '2', '3', '4', '5', 'interval', '6', '7', '8'];

        periodOrder.forEach(period => {
            const time = this.periodTimes[period];

            if (period === 'interval') {
                // Interval row
                const intervalRow = document.createElement('tr');
                intervalRow.innerHTML = `
                    <td class="period-time interval">${time}</td>
                    <td colspan="5" class="interval">INTERVAL</td>
                `;
                table.appendChild(intervalRow);
            } else {
                // Regular period row
                const row = document.createElement('tr');
                row.innerHTML = `<td class="period-time">${time}</td>`;

                this.daysOfWeek.forEach(day => {
                    const assignments = reliefData.reliefAssignments[day] && reliefData.reliefAssignments[day][period]
                        ? reliefData.reliefAssignments[day][period] : [];

                    let cellContent = '';
                    let cellClass = '';

                    if (assignments.length === 0) {
                        cellContent = '<span class="free-period">No Relief Needed</span>';
                        cellClass = 'free-period';
                    } else {
                        cellContent = assignments.map(assignment => {
                            if (assignment.type === 'unassigned') {
                                return `<div class="unassigned-relief">
                                    <strong>UNASSIGNED</strong><br>
                                    ${assignment.subject}<br>
                                    <small>(${assignment.originalTeacher})</small>
                                </div>`;
                            } else {
                                return `<div class="relief-assignment">
                                    <strong>${assignment.reliefTeacher}</strong><br>
                                    ${assignment.subject}<br>
                                    <small>(for ${assignment.originalTeacher})</small>
                                </div>`;
                            }
                        }).join('');
                        cellClass = 'relief-period';
                    }

                    row.innerHTML += `<td class="${cellClass}">${cellContent}</td>`;
                });

                table.appendChild(row);
            }
        });

        container.appendChild(table);

        // Action buttons
        const actions = document.createElement('div');
        actions.className = 'relief-actions';
        actions.innerHTML = `
            <button class="btn btn-primary" onclick="reliefManager.printReliefTimetable('${reliefData.id}')">
                Print Timetable
            </button>
            <button class="btn btn-secondary" onclick="reliefManager.exportReliefTimetable('${reliefData.id}')">
                Export
            </button>
        `;
        container.appendChild(actions);

        return container;
    }

    async printReliefTimetable(reliefId) {
        try {
            const reliefData = await db.get('reliefTimetables', reliefId);
            if (!reliefData) {
                Utils.showAlert('Relief timetable not found');
                return;
            }

            const printContainer = this.renderReliefTimetable(reliefData);
            printContainer.id = 'printableReliefTimetable';
            
            // Temporarily add to DOM for printing
            document.body.appendChild(printContainer);
            Utils.printElement('printableReliefTimetable');
            document.body.removeChild(printContainer);
        } catch (error) {
            console.error('Error printing relief timetable:', error);
            Utils.showAlert('Failed to print relief timetable');
        }
    }

    async exportReliefTimetable(reliefId) {
        try {
            const reliefData = await db.get('reliefTimetables', reliefId);
            if (!reliefData) {
                Utils.showAlert('Relief timetable not found');
                return;
            }

            const filename = `relief_timetable_${reliefData.date}.json`;
            Utils.exportToJSON(reliefData, filename);
        } catch (error) {
            console.error('Error exporting relief timetable:', error);
            Utils.showAlert('Failed to export relief timetable');
        }
    }

    renderReliefHistory(reliefTimetables) {
        const container = document.createElement('div');
        container.className = 'relief-history-container';

        if (reliefTimetables.length === 0) {
            container.innerHTML = '<p class="no-data">No relief timetables found</p>';
            return container;
        }

        const table = document.createElement('table');
        table.className = 'table';
        table.innerHTML = `
            <thead>
                <tr>
                    <th>Date</th>
                    <th>Absent Teachers</th>
                    <th>Relief Assignments</th>
                    <th>Created By</th>
                    <th>Actions</th>
                </tr>
            </thead>
            <tbody>
                ${reliefTimetables.map(relief => `
                    <tr>
                        <td>${Utils.formatDate(relief.date)}</td>
                        <td>${relief.absentTeachers.map(t => t.name).join(', ')}</td>
                        <td>${this.countReliefAssignments(relief.reliefAssignments)}</td>
                        <td>${relief.createdBy || 'Unknown'}</td>
                        <td class="action-buttons">
                            <button class="btn btn-sm btn-primary" onclick="reliefManager.viewReliefTimetable('${relief.id}')">
                                View
                            </button>
                            <button class="btn btn-sm btn-secondary" onclick="reliefManager.printReliefTimetable('${relief.id}')">
                                Print
                            </button>
                            <button class="btn btn-sm btn-danger" onclick="reliefManager.confirmDeleteRelief('${relief.id}')">
                                Delete
                            </button>
                        </td>
                    </tr>
                `).join('')}
            </tbody>
        `;

        container.appendChild(table);
        return container;
    }

    countReliefAssignments(reliefAssignments) {
        let count = 0;
        this.daysOfWeek.forEach(day => {
            Object.keys(this.periodTimes).forEach(period => {
                if (period !== 'interval' && reliefAssignments[day] && reliefAssignments[day][period]) {
                    count += reliefAssignments[day][period].length;
                }
            });
        });
        return count;
    }

    async viewReliefTimetable(reliefId) {
        try {
            const reliefData = await db.get('reliefTimetables', reliefId);
            if (!reliefData) {
                Utils.showAlert('Relief timetable not found');
                return;
            }

            // This would typically open a modal or navigate to a detailed view
            // For now, we'll just log the data
            console.log('Viewing relief timetable:', reliefData);
        } catch (error) {
            console.error('Error viewing relief timetable:', error);
            Utils.showAlert('Failed to view relief timetable');
        }
    }

    async confirmDeleteRelief(reliefId) {
        if (Utils.showConfirmDialog('Are you sure you want to delete this relief timetable?')) {
            const result = await this.deleteReliefTimetable(reliefId);
            if (result.success) {
                Utils.showAlert('Relief timetable deleted successfully');
                // Refresh the view
                if (typeof refreshReliefHistory === 'function') {
                    refreshReliefHistory();
                }
            } else {
                Utils.showAlert(result.message);
            }
        }
    }
}

// Global relief manager instance
const reliefManager = new ReliefManager();
