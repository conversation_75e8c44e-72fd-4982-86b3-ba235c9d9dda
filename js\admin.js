// Admin dashboard logic
class AdminDashboard {
    constructor() {
        this.currentTab = 'dashboard';
        this.isInitialized = false;
    }

    async init() {
        try {
            // Initialize database and auth first
            await db.init();
            await auth.init();

            // Now check authentication after initialization
            if (!auth.requireAdmin()) {
                return;
            }

            // Initialize UI
            this.initializeUI();
            this.initializeTabs();
            this.initializeSupportModal();

            // Load initial data
            await this.loadDashboardData();

            this.isInitialized = true;
            console.log('Admin dashboard initialized successfully');
        } catch (error) {
            console.error('Failed to initialize admin dashboard:', error);
            Utils.showAlert('Failed to initialize dashboard. Please refresh the page.');
        }
    }

    initializeUI() {
        // Set welcome message
        const welcomeMessage = document.getElementById('welcomeMessage');
        if (welcomeMessage) {
            const user = auth.getCurrentUser();
            welcomeMessage.textContent = `Welcome, ${user?.fullName || user?.username || 'Admin'}`;
        }

        // Logout button
        const logoutBtn = document.getElementById('logoutBtn');
        if (logoutBtn) {
            logoutBtn.addEventListener('click', () => {
                if (Utils.showConfirmDialog('Are you sure you want to logout?')) {
                    auth.logout();
                }
            });
        }

        // Set current date
        const currentDate = document.getElementById('currentDate');
        if (currentDate) {
            currentDate.textContent = Utils.getTodayFormatted();
        }

        // Set relief date to today
        const reliefDate = document.getElementById('reliefDate');
        if (reliefDate) {
            reliefDate.value = Utils.getToday();
        }
    }

    initializeTabs() {
        const navLinks = document.querySelectorAll('.nav-link');
        navLinks.forEach(link => {
            link.addEventListener('click', (e) => {
                e.preventDefault();
                const tabName = link.dataset.tab;
                this.switchTab(tabName);
            });
        });

        // Initialize button handlers for each tab
        this.initializeTeachersTab();
        this.initializeSubjectsTab();
        this.initializeTimetablesTab();
        this.initializeReliefTab();
        this.initializeUsersTab();
        this.initializeBackupTab();
    }

    switchTab(tabName) {
        // Update navigation
        document.querySelectorAll('.nav-link').forEach(link => {
            link.classList.remove('active');
        });
        document.querySelector(`[data-tab="${tabName}"]`).classList.add('active');

        // Update content
        document.querySelectorAll('.tab-content').forEach(content => {
            content.classList.remove('active');
        });
        document.getElementById(tabName).classList.add('active');

        this.currentTab = tabName;

        // Load tab-specific data
        this.loadTabData(tabName);
    }

    async loadTabData(tabName) {
        switch (tabName) {
            case 'dashboard':
                await this.loadDashboardData();
                break;
            case 'teachers':
                await this.loadTeachersData();
                break;
            case 'subjects':
                await this.loadSubjectsData();
                break;
            case 'timetables':
                await this.loadTimetablesData();
                break;
            case 'relief':
                await this.loadReliefData();
                break;
            case 'users':
                await this.loadUsersData();
                break;
            case 'backup':
                // Backup tab doesn't need data loading
                break;
        }
    }

    async loadDashboardData() {
        try {
            const teachers = await timetableManager.getAllTeachers();
            const subjects = await timetableManager.getAllSubjects();
            const reliefTimetables = await reliefManager.getAllReliefTimetables();

            // Update dashboard statistics
            document.getElementById('totalTeachers').textContent = teachers.length;
            document.getElementById('totalSubjects').textContent = subjects.length;

            // Get today's relief timetable
            const today = Utils.getToday();
            const todayRelief = reliefTimetables.find(r => r.date === today);
            document.getElementById('absentTeachers').textContent = todayRelief ? todayRelief.absentTeachers.length : 0;

            // Show recent relief timetables
            const recentRelief = document.getElementById('recentRelief');
            if (reliefTimetables.length > 0) {
                const recent = reliefTimetables.slice(0, 3);
                recentRelief.innerHTML = recent.map(relief => `
                    <div class="recent-relief-item">
                        <strong>${Utils.formatDate(relief.date)}</strong><br>
                        <small>${relief.absentTeachers.length} absent teacher(s)</small>
                    </div>
                `).join('');
            } else {
                recentRelief.innerHTML = '<p>No recent relief timetables</p>';
            }
        } catch (error) {
            console.error('Error loading dashboard data:', error);
        }
    }

    initializeTeachersTab() {
        const addTeacherBtn = document.getElementById('addTeacherBtn');
        if (addTeacherBtn) {
            addTeacherBtn.addEventListener('click', () => {
                this.showTeacherModal();
            });
        }
    }

    async loadTeachersData() {
        try {
            const teachers = await timetableManager.getAllTeachers();
            const teachersTable = document.getElementById('teachersTable');
            
            if (teachers.length === 0) {
                teachersTable.innerHTML = '<p class="no-data">No teachers found. Add your first teacher to get started.</p>';
                return;
            }

            const table = document.createElement('table');
            table.className = 'table';
            table.innerHTML = `
                <thead>
                    <tr>
                        <th>Name</th>
                        <th>Subject</th>
                        <th>Status</th>
                        <th>Created</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
                    ${teachers.map(teacher => `
                        <tr>
                            <td>${teacher.name}</td>
                            <td>${teacher.subject}</td>
                            <td>
                                <span class="status ${teacher.isActive ? 'active' : 'inactive'}">
                                    ${teacher.isActive ? 'Active' : 'Inactive'}
                                </span>
                            </td>
                            <td>${Utils.formatDate(teacher.createdAt)}</td>
                            <td class="action-buttons">
                                <button class="btn btn-sm btn-primary" onclick="adminDashboard.editTeacher(${teacher.id})">
                                    Edit
                                </button>
                                <button class="btn btn-sm btn-secondary" onclick="adminDashboard.editTeacherTimetable(${teacher.id})">
                                    Timetable
                                </button>
                                <button class="btn btn-sm btn-danger" onclick="adminDashboard.deleteTeacher(${teacher.id})">
                                    Delete
                                </button>
                            </td>
                        </tr>
                    `).join('')}
                </tbody>
            `;

            teachersTable.innerHTML = '';
            teachersTable.appendChild(table);
        } catch (error) {
            console.error('Error loading teachers data:', error);
            document.getElementById('teachersTable').innerHTML = '<p class="error">Failed to load teachers data</p>';
        }
    }

    initializeSubjectsTab() {
        const addSubjectBtn = document.getElementById('addSubjectBtn');
        if (addSubjectBtn) {
            addSubjectBtn.addEventListener('click', () => {
                this.showSubjectModal();
            });
        }
    }

    async loadSubjectsData() {
        try {
            const subjects = await timetableManager.getAllSubjects();
            const subjectsTable = document.getElementById('subjectsTable');
            
            if (subjects.length === 0) {
                subjectsTable.innerHTML = '<p class="no-data">No subjects found. Add your first subject to get started.</p>';
                return;
            }

            const table = document.createElement('table');
            table.className = 'table';
            table.innerHTML = `
                <thead>
                    <tr>
                        <th>Name</th>
                        <th>Code</th>
                        <th>Description</th>
                        <th>Created</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
                    ${subjects.map(subject => `
                        <tr>
                            <td>${subject.name}</td>
                            <td>${subject.code || '-'}</td>
                            <td>${subject.description || '-'}</td>
                            <td>${Utils.formatDate(subject.createdAt)}</td>
                            <td class="action-buttons">
                                <button class="btn btn-sm btn-primary" onclick="adminDashboard.editSubject(${subject.id})">
                                    Edit
                                </button>
                                <button class="btn btn-sm btn-danger" onclick="adminDashboard.deleteSubject(${subject.id})">
                                    Delete
                                </button>
                            </td>
                        </tr>
                    `).join('')}
                </tbody>
            `;

            subjectsTable.innerHTML = '';
            subjectsTable.appendChild(table);
        } catch (error) {
            console.error('Error loading subjects data:', error);
            document.getElementById('subjectsTable').innerHTML = '<p class="error">Failed to load subjects data</p>';
        }
    }

    initializeTimetablesTab() {
        const editTimetableBtn = document.getElementById('editTimetableBtn');
        const viewMasterTimetableBtn = document.getElementById('viewMasterTimetableBtn');
        
        if (editTimetableBtn) {
            editTimetableBtn.addEventListener('click', () => {
                const teacherSelect = document.getElementById('teacherSelect');
                const teacherId = teacherSelect.value;
                if (teacherId) {
                    this.editTeacherTimetable(parseInt(teacherId));
                } else {
                    Utils.showAlert('Please select a teacher first');
                }
            });
        }

        if (viewMasterTimetableBtn) {
            viewMasterTimetableBtn.addEventListener('click', () => {
                this.viewMasterTimetable();
            });
        }
    }

    async loadTimetablesData() {
        try {
            const teachers = await timetableManager.getAllTeachers();
            const teacherSelect = document.getElementById('teacherSelect');
            
            teacherSelect.innerHTML = '<option value="">Select Teacher</option>';
            teachers.forEach(teacher => {
                teacherSelect.innerHTML += `<option value="${teacher.id}">${teacher.name}</option>`;
            });
        } catch (error) {
            console.error('Error loading timetables data:', error);
        }
    }

    initializeSupportModal() {
        const supportBtn = document.getElementById('supportBtn');
        const supportModal = document.getElementById('supportModal');
        const closeBtn = supportModal?.querySelector('.close');

        if (supportBtn && supportModal) {
            supportBtn.addEventListener('click', () => {
                supportModal.style.display = 'block';
            });

            if (closeBtn) {
                closeBtn.addEventListener('click', () => {
                    supportModal.style.display = 'none';
                });
            }

            window.addEventListener('click', (event) => {
                if (event.target === supportModal) {
                    supportModal.style.display = 'none';
                }
            });
        }
    }

    // Teacher Management Methods
    showTeacherModal(teacherId = null) {
        const modalHtml = `
            <div class="modal" id="teacherModal">
                <div class="modal-content">
                    <span class="close">&times;</span>
                    <h3>${teacherId ? 'Edit Teacher' : 'Add New Teacher'}</h3>
                    <form id="teacherForm">
                        <div class="form-group">
                            <label for="teacherName">Name:</label>
                            <input type="text" id="teacherName" name="name" required>
                        </div>
                        <div class="form-group">
                            <label for="teacherSubject">Subject:</label>
                            <input type="text" id="teacherSubject" name="subject" required>
                        </div>
                        <div class="form-group">
                            <label>
                                <input type="checkbox" id="teacherActive" name="isActive" checked>
                                Active
                            </label>
                        </div>
                        <div class="form-group">
                            <button type="submit" class="btn btn-primary">
                                ${teacherId ? 'Update Teacher' : 'Add Teacher'}
                            </button>
                            <button type="button" class="btn btn-secondary" onclick="this.closest('.modal').style.display='none'">
                                Cancel
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        `;

        this.showModal(modalHtml, async () => {
            if (teacherId) {
                const teacher = await timetableManager.getTeacher(teacherId);
                if (teacher) {
                    document.getElementById('teacherName').value = teacher.name;
                    document.getElementById('teacherSubject').value = teacher.subject;
                    document.getElementById('teacherActive').checked = teacher.isActive;
                }
            }

            document.getElementById('teacherForm').addEventListener('submit', async (e) => {
                e.preventDefault();
                await this.handleTeacherSubmit(teacherId);
            });
        });
    }

    async handleTeacherSubmit(teacherId) {
        const formData = new FormData(document.getElementById('teacherForm'));
        const teacherData = {
            name: formData.get('name'),
            subject: formData.get('subject'),
            isActive: formData.get('isActive') === 'on'
        };

        try {
            let result;
            if (teacherId) {
                result = await timetableManager.updateTeacher(teacherId, teacherData);
            } else {
                result = await timetableManager.createTeacher(teacherData);
            }

            if (result.success) {
                Utils.showAlert(result.message);
                this.closeModal('teacherModal');
                await this.loadTeachersData();
            } else {
                Utils.showAlert(result.message);
            }
        } catch (error) {
            console.error('Error submitting teacher:', error);
            Utils.showAlert('Failed to save teacher');
        }
    }

    async editTeacher(teacherId) {
        this.showTeacherModal(teacherId);
    }

    async deleteTeacher(teacherId) {
        if (Utils.showConfirmDialog('Are you sure you want to delete this teacher?')) {
            const result = await timetableManager.deleteTeacher(teacherId);
            if (result.success) {
                Utils.showAlert(result.message);
                await this.loadTeachersData();
            } else {
                Utils.showAlert(result.message);
            }
        }
    }

    // Subject Management Methods
    showSubjectModal(subjectId = null) {
        const modalHtml = `
            <div class="modal" id="subjectModal">
                <div class="modal-content">
                    <span class="close">&times;</span>
                    <h3>${subjectId ? 'Edit Subject' : 'Add New Subject'}</h3>
                    <form id="subjectForm">
                        <div class="form-group">
                            <label for="subjectName">Name:</label>
                            <input type="text" id="subjectName" name="name" required>
                        </div>
                        <div class="form-group">
                            <label for="subjectCode">Code:</label>
                            <input type="text" id="subjectCode" name="code">
                        </div>
                        <div class="form-group">
                            <label for="subjectDescription">Description:</label>
                            <textarea id="subjectDescription" name="description" rows="3"></textarea>
                        </div>
                        <div class="form-group">
                            <button type="submit" class="btn btn-primary">
                                ${subjectId ? 'Update Subject' : 'Add Subject'}
                            </button>
                            <button type="button" class="btn btn-secondary" onclick="this.closest('.modal').style.display='none'">
                                Cancel
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        `;

        this.showModal(modalHtml, async () => {
            if (subjectId) {
                const subject = await db.get('subjects', subjectId);
                if (subject) {
                    document.getElementById('subjectName').value = subject.name;
                    document.getElementById('subjectCode').value = subject.code || '';
                    document.getElementById('subjectDescription').value = subject.description || '';
                }
            }

            document.getElementById('subjectForm').addEventListener('submit', async (e) => {
                e.preventDefault();
                await this.handleSubjectSubmit(subjectId);
            });
        });
    }

    async handleSubjectSubmit(subjectId) {
        const formData = new FormData(document.getElementById('subjectForm'));
        const subjectData = {
            name: formData.get('name'),
            code: formData.get('code'),
            description: formData.get('description')
        };

        try {
            let result;
            if (subjectId) {
                result = await timetableManager.updateSubject(subjectId, subjectData);
            } else {
                result = await timetableManager.createSubject(subjectData);
            }

            if (result.success) {
                Utils.showAlert(result.message);
                this.closeModal('subjectModal');
                await this.loadSubjectsData();
            } else {
                Utils.showAlert(result.message);
            }
        } catch (error) {
            console.error('Error submitting subject:', error);
            Utils.showAlert('Failed to save subject');
        }
    }

    async editSubject(subjectId) {
        this.showSubjectModal(subjectId);
    }

    async deleteSubject(subjectId) {
        if (Utils.showConfirmDialog('Are you sure you want to delete this subject?')) {
            const result = await timetableManager.deleteSubject(subjectId);
            if (result.success) {
                Utils.showAlert(result.message);
                await this.loadSubjectsData();
            } else {
                Utils.showAlert(result.message);
            }
        }
    }

    // Utility Methods
    showModal(modalHtml, onShow = null) {
        const dynamicModals = document.getElementById('dynamicModals');
        dynamicModals.innerHTML = modalHtml;

        const modal = dynamicModals.querySelector('.modal');
        const closeBtn = modal.querySelector('.close');

        if (closeBtn) {
            closeBtn.addEventListener('click', () => {
                modal.style.display = 'none';
            });
        }

        window.addEventListener('click', (event) => {
            if (event.target === modal) {
                modal.style.display = 'none';
            }
        });

        modal.style.display = 'block';

        if (onShow) {
            onShow();
        }
    }

    closeModal(modalId) {
        const modal = document.getElementById(modalId);
        if (modal) {
            modal.style.display = 'none';
        }
    }

    // Timetable Management Methods
    async editTeacherTimetable(teacherId) {
        const teacher = await timetableManager.getTeacher(teacherId);
        if (!teacher) {
            Utils.showAlert('Teacher not found');
            return;
        }

        const modalHtml = `
            <div class="modal" id="timetableModal" style="z-index: 1001;">
                <div class="modal-content" style="max-width: 90%; width: 1000px;">
                    <span class="close">&times;</span>
                    <h3>Edit Timetable - ${teacher.name}</h3>
                    <div id="timetableEditor"></div>
                    <div class="form-group" style="margin-top: 20px;">
                        <button type="button" class="btn btn-primary" onclick="adminDashboard.saveTimetable(${teacherId})">
                            Save Timetable
                        </button>
                        <button type="button" class="btn btn-secondary" onclick="adminDashboard.closeModal('timetableModal')">
                            Cancel
                        </button>
                    </div>
                </div>
            </div>
        `;

        this.showModal(modalHtml, () => {
            const timetableEditor = document.getElementById('timetableEditor');
            const timetableGrid = timetableManager.renderTimetableGrid(teacher.timetable, true, teacherId);
            timetableEditor.appendChild(timetableGrid);
        });
    }

    async saveTimetable(teacherId) {
        const timetableEditor = document.getElementById('timetableEditor');
        const timetable = timetableManager.extractTimetableFromForm(timetableEditor);

        const result = await timetableManager.updateTeacherTimetable(teacherId, timetable);
        if (result.success) {
            Utils.showAlert(result.message);
            this.closeModal('timetableModal');
        } else {
            Utils.showAlert(result.message);
        }
    }

    async viewMasterTimetable() {
        const masterTimetable = await timetableManager.generateMasterTimetable();
        const timetableContainer = document.getElementById('timetableContainer');

        timetableContainer.innerHTML = '<h3>Master Timetable</h3>';
        const masterGrid = timetableManager.renderMasterTimetable(masterTimetable);
        timetableContainer.appendChild(masterGrid);
    }

    // Relief Management Methods
    initializeReliefTab() {
        const createReliefBtn = document.getElementById('createReliefBtn');
        const viewReliefHistoryBtn = document.getElementById('viewReliefHistoryBtn');

        if (createReliefBtn) {
            createReliefBtn.addEventListener('click', () => {
                this.showCreateReliefModal();
            });
        }

        if (viewReliefHistoryBtn) {
            viewReliefHistoryBtn.addEventListener('click', () => {
                this.showReliefHistory();
            });
        }
    }

    async loadReliefData() {
        // Load today's relief if exists
        const today = Utils.getToday();
        const todayRelief = await reliefManager.getReliefTimetableByDate(today);

        const reliefContainer = document.getElementById('reliefContainer');
        if (todayRelief) {
            reliefContainer.innerHTML = '<h3>Today\'s Relief Timetable</h3>';
            const reliefDisplay = reliefManager.renderReliefTimetable(todayRelief);
            reliefContainer.appendChild(reliefDisplay);
        } else {
            reliefContainer.innerHTML = '<p class="no-data">No relief timetable for today. Create one using the controls above.</p>';
        }
    }

    async showCreateReliefModal() {
        const teachers = await timetableManager.getAllTeachers();
        const reliefDate = document.getElementById('reliefDate').value;

        if (!reliefDate) {
            Utils.showAlert('Please select a date first');
            return;
        }

        const modalHtml = `
            <div class="modal" id="reliefModal">
                <div class="modal-content" style="max-width: 600px;">
                    <span class="close">&times;</span>
                    <h3>Create Relief Timetable for ${Utils.formatDate(reliefDate)}</h3>
                    <form id="reliefForm">
                        <div class="form-group">
                            <label>Select Absent Teachers:</label>
                            <div class="teacher-checkboxes" style="max-height: 300px; overflow-y: auto; border: 1px solid #ddd; padding: 10px;">
                                ${teachers.map(teacher => `
                                    <label style="display: block; margin: 5px 0;">
                                        <input type="checkbox" name="absentTeachers" value="${teacher.id}">
                                        ${teacher.name} (${teacher.subject})
                                    </label>
                                `).join('')}
                            </div>
                        </div>
                        <div class="form-group">
                            <button type="submit" class="btn btn-success">Create Relief Timetable</button>
                            <button type="button" class="btn btn-secondary" onclick="adminDashboard.closeModal('reliefModal')">
                                Cancel
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        `;

        this.showModal(modalHtml, () => {
            document.getElementById('reliefForm').addEventListener('submit', async (e) => {
                e.preventDefault();
                await this.handleReliefSubmit(reliefDate);
            });
        });
    }

    async handleReliefSubmit(date) {
        const formData = new FormData(document.getElementById('reliefForm'));
        const absentTeacherIds = formData.getAll('absentTeachers').map(id => parseInt(id));

        if (absentTeacherIds.length === 0) {
            Utils.showAlert('Please select at least one absent teacher');
            return;
        }

        try {
            const result = await reliefManager.createReliefTimetable(date, absentTeacherIds);
            if (result.success) {
                Utils.showAlert(result.message);
                this.closeModal('reliefModal');
                await this.loadReliefData();
            } else {
                Utils.showAlert(result.message);
            }
        } catch (error) {
            console.error('Error creating relief timetable:', error);
            Utils.showAlert('Failed to create relief timetable');
        }
    }

    async showReliefHistory() {
        const reliefTimetables = await reliefManager.getAllReliefTimetables();
        const reliefContainer = document.getElementById('reliefContainer');

        reliefContainer.innerHTML = '<h3>Relief Timetable History</h3>';
        const historyDisplay = reliefManager.renderReliefHistory(reliefTimetables);
        reliefContainer.appendChild(historyDisplay);
    }

    // Users Management Methods
    initializeUsersTab() {
        const addUserBtn = document.getElementById('addUserBtn');
        if (addUserBtn) {
            addUserBtn.addEventListener('click', () => {
                this.showUserModal();
            });
        }
    }

    async loadUsersData() {
        try {
            const users = await auth.getAllUsers();
            const usersTable = document.getElementById('usersTable');

            if (users.length === 0) {
                usersTable.innerHTML = '<p class="no-data">No users found. Add your first user to get started.</p>';
                return;
            }

            const table = document.createElement('table');
            table.className = 'table';
            table.innerHTML = `
                <thead>
                    <tr>
                        <th>Username</th>
                        <th>Full Name</th>
                        <th>Created</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
                    ${users.map(user => `
                        <tr>
                            <td>${user.username}</td>
                            <td>${user.fullName}</td>
                            <td>${Utils.formatDate(user.createdAt)}</td>
                            <td class="action-buttons">
                                <button class="btn btn-sm btn-primary" onclick="adminDashboard.editUser(${user.id})">
                                    Edit
                                </button>
                                <button class="btn btn-sm btn-danger" onclick="adminDashboard.deleteUser(${user.id})">
                                    Delete
                                </button>
                            </td>
                        </tr>
                    `).join('')}
                </tbody>
            `;

            usersTable.innerHTML = '';
            usersTable.appendChild(table);
        } catch (error) {
            console.error('Error loading users data:', error);
            document.getElementById('usersTable').innerHTML = '<p class="error">Failed to load users data</p>';
        }
    }

    showUserModal(userId = null) {
        const modalHtml = `
            <div class="modal" id="userModal">
                <div class="modal-content">
                    <span class="close">&times;</span>
                    <h3>${userId ? 'Edit User' : 'Add New User'}</h3>
                    <form id="userForm">
                        <div class="form-group">
                            <label for="userUsername">Username:</label>
                            <input type="text" id="userUsername" name="username" required ${userId ? 'readonly' : ''}>
                        </div>
                        <div class="form-group">
                            <label for="userFullName">Full Name:</label>
                            <input type="text" id="userFullName" name="fullName" required>
                        </div>
                        <div class="form-group">
                            <label for="userPassword">Password:</label>
                            <input type="password" id="userPassword" name="password" ${userId ? '' : 'required'}>
                            ${userId ? '<small>Leave blank to keep current password</small>' : ''}
                        </div>
                        <div class="form-group">
                            <button type="submit" class="btn btn-primary">
                                ${userId ? 'Update User' : 'Add User'}
                            </button>
                            <button type="button" class="btn btn-secondary" onclick="adminDashboard.closeModal('userModal')">
                                Cancel
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        `;

        this.showModal(modalHtml, async () => {
            if (userId) {
                const user = await db.get('users', userId);
                if (user) {
                    document.getElementById('userUsername').value = user.username;
                    document.getElementById('userFullName').value = user.fullName;
                }
            }

            document.getElementById('userForm').addEventListener('submit', async (e) => {
                e.preventDefault();
                await this.handleUserSubmit(userId);
            });
        });
    }

    async handleUserSubmit(userId) {
        const formData = new FormData(document.getElementById('userForm'));
        const userData = {
            username: formData.get('username'),
            fullName: formData.get('fullName'),
            password: formData.get('password')
        };

        try {
            let result;
            if (userId) {
                result = await auth.updateUser(userId, userData);
            } else {
                result = await auth.createUser(userData);
            }

            if (result.success) {
                Utils.showAlert(result.message);
                this.closeModal('userModal');
                await this.loadUsersData();
            } else {
                Utils.showAlert(result.message);
            }
        } catch (error) {
            console.error('Error submitting user:', error);
            Utils.showAlert('Failed to save user');
        }
    }

    async editUser(userId) {
        this.showUserModal(userId);
    }

    async deleteUser(userId) {
        if (Utils.showConfirmDialog('Are you sure you want to delete this user?')) {
            const result = await auth.deleteUser(userId);
            if (result.success) {
                Utils.showAlert(result.message);
                await this.loadUsersData();
            } else {
                Utils.showAlert(result.message);
            }
        }
    }

    // Backup Management Methods
    initializeBackupTab() {
        const exportDataBtn = document.getElementById('exportDataBtn');
        const importDataBtn = document.getElementById('importDataBtn');
        const importFileInput = document.getElementById('importFileInput');

        if (exportDataBtn) {
            exportDataBtn.addEventListener('click', () => {
                this.exportData();
            });
        }

        if (importDataBtn) {
            importDataBtn.addEventListener('click', () => {
                importFileInput.click();
            });
        }

        if (importFileInput) {
            importFileInput.addEventListener('change', (e) => {
                if (e.target.files.length > 0) {
                    this.importData(e.target.files[0]);
                }
            });
        }
    }

    async exportData() {
        try {
            const data = await db.exportData();
            const filename = `school_timetable_backup_${Utils.getToday()}.json`;
            Utils.exportToJSON(data, filename);
            Utils.showAlert('Data exported successfully');
        } catch (error) {
            console.error('Error exporting data:', error);
            Utils.showAlert('Failed to export data');
        }
    }

    async importData(file) {
        try {
            const data = await Utils.importFromJSON(file);

            if (Utils.showConfirmDialog('This will replace all existing data. Are you sure you want to continue?')) {
                await db.importData(data);
                Utils.showAlert('Data imported successfully. Please refresh the page.');

                // Refresh current tab data
                await this.loadTabData(this.currentTab);
            }
        } catch (error) {
            console.error('Error importing data:', error);
            Utils.showAlert('Failed to import data. Please check the file format.');
        }
    }
}

// Global admin dashboard instance
const adminDashboard = new AdminDashboard();

// Global functions for onclick handlers
window.switchTab = (tabName) => adminDashboard.switchTab(tabName);

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', async () => {
    await adminDashboard.init();
});
