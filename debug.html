<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Debug Login</title>
</head>
<body>
    <h1>Debug Login Test</h1>
    <div id="status"></div>
    <button onclick="testLogin()">Test Login</button>
    <button onclick="testAPI()">Test API Direct</button>

    <script src="js/database.js"></script>
    <script src="js/auth.js"></script>
    <script src="js/utils.js"></script>
    
    <script>
        async function init() {
            try {
                console.log('Initializing...');
                await db.init();
                console.log('Database initialized');
                await auth.init();
                console.log('Auth initialized');
                document.getElementById('status').innerHTML = 'Initialized successfully';
            } catch (error) {
                console.error('Init error:', error);
                document.getElementById('status').innerHTML = `Init error: ${error.message}`;
            }
        }

        async function testLogin() {
            try {
                console.log('Testing login...');
                const result = await auth.login('admin', 'admin123', 'admin');
                console.log('Login result:', result);
                document.getElementById('status').innerHTML = `Login result: ${JSON.stringify(result, null, 2)}`;
            } catch (error) {
                console.error('Login error:', error);
                document.getElementById('status').innerHTML = `Login error: ${error.message}`;
            }
        }

        async function testAPI() {
            try {
                console.log('Testing API direct...');
                const response = await fetch('/api/auth/login', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        username: 'admin',
                        password: 'admin123',
                        userType: 'admin'
                    })
                });
                
                const data = await response.json();
                console.log('API result:', data);
                document.getElementById('status').innerHTML = `API result: ${JSON.stringify(data, null, 2)}`;
            } catch (error) {
                console.error('API error:', error);
                document.getElementById('status').innerHTML = `API error: ${error.message}`;
            }
        }

        // Initialize when page loads
        init();
    </script>
</body>
</html>
