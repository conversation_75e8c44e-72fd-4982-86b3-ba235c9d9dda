<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>School Relief Time Table Creator</title>
    <link rel="stylesheet" href="css/styles.css">
    <link rel="manifest" href="manifest.json">
    <meta name="theme-color" content="#2196F3">
</head>
<body>
    <div class="container">
        <header class="header">
            <h1>School Relief Time Table Creator</h1>
            <p class="subtitle">Manage your school timetables efficiently</p>
        </header>

        <main class="main-content">
            <div class="login-container">
                <div class="login-form">
                    <h2>Login</h2>
                    <form id="loginForm">
                        <div class="form-group">
                            <label for="username">Username:</label>
                            <input type="text" id="username" name="username" required>
                        </div>
                        <div class="form-group">
                            <label for="password">Password:</label>
                            <input type="password" id="password" name="password" required>
                        </div>
                        <div class="form-group">
                            <label for="userType">Login as:</label>
                            <select id="userType" name="userType" required>
                                <option value="">Select user type</option>
                                <option value="admin">Admin</option>
                                <option value="user">User</option>
                            </select>
                        </div>
                        <button type="submit" class="btn btn-primary">Login</button>
                    </form>
                    <div id="loginMessage" class="message"></div>
                </div>
            </div>
        </main>

        <footer class="footer">
            <p>Design and developed by - Infotech Solutions - 0717754244</p>
            <button id="supportBtn" class="support-btn">Support</button>
        </footer>
    </div>

    <!-- Support Modal -->
    <div id="supportModal" class="modal">
        <div class="modal-content">
            <span class="close">&times;</span>
            <h3>Support Information</h3>
            <div class="support-info">
                <p><strong>Name:</strong> NAP Dharmapriya</p>
                <p><strong>Contact:</strong> 0717754244</p>
                <p><strong>Email:</strong> <EMAIL></p>
                <p><strong>Facebook:</strong> www.fb.com/pavidlk</p>
            </div>
        </div>
    </div>

    <script src="js/database.js"></script>
    <script src="js/auth.js"></script>
    <script src="js/utils.js"></script>
    <script src="js/app.js"></script>
</body>
</html>
