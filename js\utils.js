// Utility functions
class Utils {
    static showMessage(elementId, message, type = 'info') {
        const element = document.getElementById(elementId);
        if (element) {
            element.textContent = message;
            element.className = `message ${type}`;
            element.style.display = 'block';
            
            // Auto-hide after 5 seconds
            setTimeout(() => {
                element.style.display = 'none';
            }, 5000);
        }
    }

    static clearMessage(elementId) {
        const element = document.getElementById(elementId);
        if (element) {
            element.style.display = 'none';
        }
    }

    static formatDate(date) {
        if (!(date instanceof Date)) {
            date = new Date(date);
        }
        return date.toLocaleDateString('en-US', {
            year: 'numeric',
            month: '2-digit',
            day: '2-digit'
        });
    }

    static formatTime(timeString) {
        // Convert time like "7.45" to "07:45"
        const parts = timeString.split('.');
        const hours = parts[0].padStart(2, '0');
        const minutes = parts[1] || '00';
        return `${hours}:${minutes}`;
    }

    static getPeriodTimes() {
        return {
            '1': '7.45 - 8.30',
            '2': '8.30 - 9.10',
            '3': '9.10 - 9.50',
            '4': '9.50 - 10.30',
            '5': '10.30 - 11.10',
            'interval': '11.10 - 11.30',
            '6': '11.30 - 12.10',
            '7': '12.10 - 12.50',
            '8': '12.50 - 1.30'
        };
    }

    static getDaysOfWeek() {
        return ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday'];
    }

    static createEmptyTimetable() {
        const days = this.getDaysOfWeek();
        const timetable = {};
        
        days.forEach(day => {
            timetable[day] = {
                '1': '',
                '2': '',
                '3': '',
                '4': '',
                '5': '',
                '6': '',
                '7': '',
                '8': ''
            };
        });
        
        return timetable;
    }

    static validateTimetable(timetable) {
        const days = this.getDaysOfWeek();
        const periods = ['1', '2', '3', '4', '5', '6', '7', '8'];
        
        for (const day of days) {
            if (!timetable[day]) {
                return false;
            }
            for (const period of periods) {
                if (timetable[day][period] === undefined) {
                    return false;
                }
            }
        }
        return true;
    }

    static exportToJSON(data, filename) {
        const jsonString = JSON.stringify(data, null, 2);
        const blob = new Blob([jsonString], { type: 'application/json' });
        const url = URL.createObjectURL(blob);
        
        const a = document.createElement('a');
        a.href = url;
        a.download = filename;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);
    }

    static importFromJSON(file) {
        return new Promise((resolve, reject) => {
            const reader = new FileReader();
            reader.onload = (e) => {
                try {
                    const data = JSON.parse(e.target.result);
                    resolve(data);
                } catch (error) {
                    reject(new Error('Invalid JSON file'));
                }
            };
            reader.onerror = () => reject(new Error('Failed to read file'));
            reader.readAsText(file);
        });
    }

    static printElement(elementId) {
        const element = document.getElementById(elementId);
        if (!element) return;

        const printWindow = window.open('', '_blank');
        printWindow.document.write(`
            <!DOCTYPE html>
            <html>
            <head>
                <title>Print Timetable</title>
                <style>
                    body { font-family: Arial, sans-serif; margin: 20px; }
                    table { width: 100%; border-collapse: collapse; margin: 20px 0; }
                    th, td { border: 1px solid #ddd; padding: 8px; text-align: center; }
                    th { background-color: #f2f2f2; font-weight: bold; }
                    .header { text-align: center; margin-bottom: 20px; }
                    .date { font-size: 14px; color: #666; }
                    @media print {
                        body { margin: 0; }
                        .no-print { display: none; }
                    }
                </style>
            </head>
            <body>
                <div class="header">
                    <h1>School Relief Timetable</h1>
                    <div class="date">Generated on: ${new Date().toLocaleDateString()}</div>
                </div>
                ${element.innerHTML}
            </body>
            </html>
        `);
        printWindow.document.close();
        printWindow.print();
    }

    static generateId() {
        return Date.now().toString(36) + Math.random().toString(36).substr(2);
    }

    static debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    }

    static createElement(tag, className = '', innerHTML = '') {
        const element = document.createElement(tag);
        if (className) element.className = className;
        if (innerHTML) element.innerHTML = innerHTML;
        return element;
    }

    static removeAllChildren(element) {
        while (element.firstChild) {
            element.removeChild(element.firstChild);
        }
    }

    static isValidEmail(email) {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        return emailRegex.test(email);
    }

    static capitalizeFirst(str) {
        return str.charAt(0).toUpperCase() + str.slice(1).toLowerCase();
    }

    static sanitizeInput(input) {
        return input.trim().replace(/[<>]/g, '');
    }

    static getToday() {
        return new Date().toISOString().split('T')[0];
    }

    static getTodayFormatted() {
        return this.formatDate(new Date());
    }

    static copyToClipboard(text) {
        if (navigator.clipboard) {
            return navigator.clipboard.writeText(text);
        } else {
            // Fallback for older browsers
            const textArea = document.createElement('textarea');
            textArea.value = text;
            document.body.appendChild(textArea);
            textArea.select();
            document.execCommand('copy');
            document.body.removeChild(textArea);
            return Promise.resolve();
        }
    }

    static showConfirmDialog(message) {
        return confirm(message);
    }

    static showAlert(message) {
        alert(message);
    }
}
