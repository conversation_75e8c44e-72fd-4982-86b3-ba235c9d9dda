# School Relief Time Table Creator

A comprehensive offline web-based application for managing school relief timetables efficiently.

## Features

### 🔐 Authentication System
- **Admin Login**: Username: `admin`, Password: `admin123`
- **User Management**: Admin can create normal user accounts with limited access
- **Role-based Access**: Different interfaces for admin and regular users

### 👨‍🏫 Teacher Management
- Add, edit, and delete teachers
- Assign subjects to teachers
- Manage teacher status (active/inactive)
- Individual teacher timetable management

### 📚 Subject Management
- Add, edit, and delete subjects
- Subject codes and descriptions
- Easy subject assignment to teachers

### 📅 Timetable Management
- **Period Times**:
  - 1st Period: 7:45
  - 2nd Period: 8:30
  - 3rd Period: 9:10
  - 4th Period: 9:50
  - 5th Period: 10:30
  - **Interval**: 11:10 - 11:30
  - 6th Period: 11:30
  - 7th Period: 12:10
  - 8th Period: 12:50

- **Days**: Monday to Friday (8 periods daily)
- Individual teacher timetable editing
- Master timetable generation from all teacher timetables

### 🔄 Relief Timetable System
- Select absent teachers for any date
- Automatic redistribution of periods among available teachers
- Equal distribution algorithm to balance workload
- Conflict detection (prevents double assignments)
- Color-coded relief assignments

### 📊 Dashboard Features
- Today's summary with statistics
- Quick action buttons
- Recent relief timetables overview
- Absent teacher count for today

### 💾 Data Management
- **Offline Storage**: All data saved in browser's IndexedDB
- **Backup/Export**: Export all data to JSON file
- **Import/Restore**: Import data from JSON backup
- **Full Offline Capability**: Works without internet connection

### 🖨️ Additional Features
- **Print Functionality**: Print relief timetables
- **PWA Support**: Install as desktop/mobile app
- **Responsive Design**: Works on all devices
- **Color Coding**: Visual indicators for different period types
- **Support Information**: Built-in contact details

## Getting Started

### 1. Setup
1. Download all files to a folder
2. Start a local web server:
   ```bash
   # Using Python
   python -m http.server 8000
   
   # Using Node.js
   npx serve .
   
   # Using PHP
   php -S localhost:8000
   ```
3. Open `http://localhost:8000` in your browser

### 2. First Login
- **Admin Access**: 
  - Username: `admin`
  - Password: `admin123`
  - User Type: Admin

### 3. Initial Setup
1. **Add Subjects**: Go to Subjects tab and add your school subjects
2. **Add Teachers**: Go to Teachers tab and add teachers with their subjects
3. **Create Timetables**: Edit individual teacher timetables
4. **Create Users**: Add normal user accounts for staff access

## Usage Guide

### For Administrators

#### Managing Teachers
1. Go to **Teachers** tab
2. Click **Add New Teacher**
3. Fill in name and subject
4. Edit timetables by clicking **Timetable** button
5. Use the timetable grid to assign subjects to periods

#### Creating Relief Timetables
1. Go to **Relief** tab
2. Select the date
3. Click **Create Relief**
4. Select absent teachers
5. System automatically redistributes their periods

#### Backup & Restore
1. Go to **Backup** tab
2. **Export**: Download JSON backup file
3. **Import**: Upload JSON file to restore data

### For Regular Users

#### Viewing Timetables
1. Go to **View Timetables** tab
2. Select a teacher to view their timetable
3. View master timetable showing all assignments

#### Creating Relief
1. Go to **Relief Management** tab
2. Select date and absent teachers
3. System creates optimized relief assignments

#### Viewing History
1. Go to **Relief History** tab
2. View all past relief timetables
3. Print or export specific dates

## Technical Details

### Technologies Used
- **Frontend**: HTML5, CSS3, Vanilla JavaScript
- **Storage**: IndexedDB for offline data persistence
- **PWA**: Service Worker for offline functionality
- **Responsive**: CSS Grid and Flexbox for mobile support

### Browser Compatibility
- Chrome 60+
- Firefox 55+
- Safari 11+
- Edge 79+

### File Structure
```
school-relief-timetable/
├── index.html          # Login page
├── admin.html          # Admin dashboard
├── user.html           # User dashboard
├── manifest.json       # PWA manifest
├── sw.js              # Service worker
├── css/
│   ├── styles.css     # Main styles
│   └── admin.css      # Dashboard styles
└── js/
    ├── app.js         # Main application
    ├── auth.js        # Authentication
    ├── database.js    # IndexedDB operations
    ├── timetable.js   # Timetable management
    ├── relief.js      # Relief system
    ├── admin.js       # Admin dashboard
    ├── user.js        # User dashboard
    └── utils.js       # Utility functions
```

## Support

**Developer**: NAP Dharmapriya  
**Contact**: **********  
**Email**: <EMAIL>  
**Facebook**: www.fb.com/pavidlk  

**Company**: Infotech Solutions - **********

## License

This project is developed for educational and school management purposes.

---

*Design and developed by - Infotech Solutions - ***********
