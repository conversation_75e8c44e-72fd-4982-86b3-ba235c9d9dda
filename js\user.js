// User dashboard logic
class UserDashboard {
    constructor() {
        this.currentTab = 'dashboard';
        this.isInitialized = false;
    }

    async init() {
        try {
            // Initialize database and auth first
            await db.init();
            await auth.init();

            // Now check authentication after initialization
            if (!auth.requireAuth()) {
                return;
            }

            // Initialize UI
            this.initializeUI();
            this.initializeTabs();
            this.initializeSupportModal();

            // Load initial data
            await this.loadDashboardData();

            this.isInitialized = true;
            console.log('User dashboard initialized successfully');
        } catch (error) {
            console.error('Failed to initialize user dashboard:', error);
            Utils.showAlert('Failed to initialize dashboard. Please refresh the page.');
        }
    }

    initializeUI() {
        // Set welcome message
        const welcomeMessage = document.getElementById('welcomeMessage');
        if (welcomeMessage) {
            const user = auth.getCurrentUser();
            welcomeMessage.textContent = `Welcome, ${user?.fullName || user?.username || 'User'}`;
        }

        // Logout button
        const logoutBtn = document.getElementById('logoutBtn');
        if (logoutBtn) {
            logoutBtn.addEventListener('click', () => {
                if (Utils.showConfirmDialog('Are you sure you want to logout?')) {
                    auth.logout();
                }
            });
        }

        // Set current date
        const currentDate = document.getElementById('currentDate');
        if (currentDate) {
            currentDate.textContent = Utils.getTodayFormatted();
        }

        // Set relief date to today
        const reliefDate = document.getElementById('reliefDate');
        if (reliefDate) {
            reliefDate.value = Utils.getToday();
        }
    }

    initializeTabs() {
        const navLinks = document.querySelectorAll('.nav-link');
        navLinks.forEach(link => {
            link.addEventListener('click', (e) => {
                e.preventDefault();
                const tabName = link.dataset.tab;
                this.switchTab(tabName);
            });
        });

        // Initialize button handlers for each tab
        this.initializeTimetablesTab();
        this.initializeReliefTab();
        this.initializeHistoryTab();
    }

    switchTab(tabName) {
        // Update navigation
        document.querySelectorAll('.nav-link').forEach(link => {
            link.classList.remove('active');
        });
        document.querySelector(`[data-tab="${tabName}"]`).classList.add('active');

        // Update content
        document.querySelectorAll('.tab-content').forEach(content => {
            content.classList.remove('active');
        });
        document.getElementById(tabName).classList.add('active');

        this.currentTab = tabName;

        // Load tab-specific data
        this.loadTabData(tabName);
    }

    async loadTabData(tabName) {
        switch (tabName) {
            case 'dashboard':
                await this.loadDashboardData();
                break;
            case 'timetables':
                await this.loadTimetablesData();
                break;
            case 'relief':
                await this.loadReliefData();
                break;
            case 'history':
                await this.loadHistoryData();
                break;
        }
    }

    async loadDashboardData() {
        try {
            const teachers = await timetableManager.getAllTeachers();
            const reliefTimetables = await reliefManager.getAllReliefTimetables();

            // Update dashboard statistics
            document.getElementById('totalTeachers').textContent = teachers.length;

            // Get today's relief timetable
            const today = Utils.getToday();
            const todayRelief = reliefTimetables.find(r => r.date === today);
            document.getElementById('absentTeachers').textContent = todayRelief ? todayRelief.absentTeachers.length : 0;

            // Show today's relief status
            const todayReliefStatus = document.getElementById('todayReliefStatus');
            if (todayRelief) {
                todayReliefStatus.innerHTML = `
                    <p><strong>Relief Created:</strong> Yes</p>
                    <p><strong>Absent Teachers:</strong> ${todayRelief.absentTeachers.map(t => t.name).join(', ')}</p>
                    <button class="btn btn-sm btn-primary" onclick="userDashboard.viewTodayRelief()">View Details</button>
                `;
            } else {
                todayReliefStatus.innerHTML = '<p>No relief timetable for today</p>';
            }
        } catch (error) {
            console.error('Error loading dashboard data:', error);
        }
    }

    initializeTimetablesTab() {
        const viewTimetableBtn = document.getElementById('viewTimetableBtn');
        const viewMasterTimetableBtn = document.getElementById('viewMasterTimetableBtn');
        
        if (viewTimetableBtn) {
            viewTimetableBtn.addEventListener('click', () => {
                const teacherSelect = document.getElementById('teacherSelect');
                const teacherId = teacherSelect.value;
                if (teacherId) {
                    this.viewTeacherTimetable(parseInt(teacherId));
                } else {
                    Utils.showAlert('Please select a teacher first');
                }
            });
        }

        if (viewMasterTimetableBtn) {
            viewMasterTimetableBtn.addEventListener('click', () => {
                this.viewMasterTimetable();
            });
        }
    }

    async loadTimetablesData() {
        try {
            const teachers = await timetableManager.getAllTeachers();
            const teacherSelect = document.getElementById('teacherSelect');
            
            teacherSelect.innerHTML = '<option value="">Select Teacher</option>';
            teachers.forEach(teacher => {
                teacherSelect.innerHTML += `<option value="${teacher.id}">${teacher.name} (${teacher.subject})</option>`;
            });
        } catch (error) {
            console.error('Error loading timetables data:', error);
        }
    }

    async viewTeacherTimetable(teacherId) {
        try {
            const teacher = await timetableManager.getTeacher(teacherId);
            if (!teacher) {
                Utils.showAlert('Teacher not found');
                return;
            }

            const timetableContainer = document.getElementById('timetableContainer');
            timetableContainer.innerHTML = `<h3>${teacher.name}'s Timetable (${teacher.subject})</h3>`;
            
            const timetableGrid = timetableManager.renderTimetableGrid(teacher.timetable, false);
            timetableContainer.appendChild(timetableGrid);
        } catch (error) {
            console.error('Error viewing teacher timetable:', error);
            Utils.showAlert('Failed to load teacher timetable');
        }
    }

    async viewMasterTimetable() {
        try {
            const masterTimetable = await timetableManager.generateMasterTimetable();
            const timetableContainer = document.getElementById('timetableContainer');
            
            timetableContainer.innerHTML = '<h3>Master Timetable</h3>';
            const masterGrid = timetableManager.renderMasterTimetable(masterTimetable);
            timetableContainer.appendChild(masterGrid);
        } catch (error) {
            console.error('Error viewing master timetable:', error);
            Utils.showAlert('Failed to load master timetable');
        }
    }

    initializeReliefTab() {
        const createReliefBtn = document.getElementById('createReliefBtn');
        
        if (createReliefBtn) {
            createReliefBtn.addEventListener('click', () => {
                this.showCreateReliefModal();
            });
        }
    }

    async loadReliefData() {
        // Load today's relief if exists
        const today = Utils.getToday();
        const todayRelief = await reliefManager.getReliefTimetableByDate(today);
        
        const reliefContainer = document.getElementById('reliefContainer');
        if (todayRelief) {
            reliefContainer.innerHTML = '<h3>Today\'s Relief Timetable</h3>';
            const reliefDisplay = reliefManager.renderReliefTimetable(todayRelief);
            reliefContainer.appendChild(reliefDisplay);
        } else {
            reliefContainer.innerHTML = '<p class="no-data">No relief timetable for today. Create one using the controls above.</p>';
        }
    }

    async showCreateReliefModal() {
        const teachers = await timetableManager.getAllTeachers();
        const reliefDate = document.getElementById('reliefDate').value;
        
        if (!reliefDate) {
            Utils.showAlert('Please select a date first');
            return;
        }

        const modalHtml = `
            <div class="modal" id="reliefModal">
                <div class="modal-content" style="max-width: 600px;">
                    <span class="close">&times;</span>
                    <h3>Create Relief Timetable for ${Utils.formatDate(reliefDate)}</h3>
                    <form id="reliefForm">
                        <div class="form-group">
                            <label>Select Absent Teachers:</label>
                            <div class="teacher-checkboxes" style="max-height: 300px; overflow-y: auto; border: 1px solid #ddd; padding: 10px;">
                                ${teachers.map(teacher => `
                                    <label style="display: block; margin: 5px 0;">
                                        <input type="checkbox" name="absentTeachers" value="${teacher.id}">
                                        ${teacher.name} (${teacher.subject})
                                    </label>
                                `).join('')}
                            </div>
                        </div>
                        <div class="form-group">
                            <button type="submit" class="btn btn-success">Create Relief Timetable</button>
                            <button type="button" class="btn btn-secondary" onclick="userDashboard.closeModal('reliefModal')">
                                Cancel
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        `;

        this.showModal(modalHtml, () => {
            document.getElementById('reliefForm').addEventListener('submit', async (e) => {
                e.preventDefault();
                await this.handleReliefSubmit(reliefDate);
            });
        });
    }

    async handleReliefSubmit(date) {
        const formData = new FormData(document.getElementById('reliefForm'));
        const absentTeacherIds = formData.getAll('absentTeachers').map(id => parseInt(id));
        
        if (absentTeacherIds.length === 0) {
            Utils.showAlert('Please select at least one absent teacher');
            return;
        }

        try {
            const result = await reliefManager.createReliefTimetable(date, absentTeacherIds);
            if (result.success) {
                Utils.showAlert(result.message);
                this.closeModal('reliefModal');
                await this.loadReliefData();
                await this.loadDashboardData();
            } else {
                Utils.showAlert(result.message);
            }
        } catch (error) {
            console.error('Error creating relief timetable:', error);
            Utils.showAlert('Failed to create relief timetable');
        }
    }

    initializeHistoryTab() {
        const refreshHistoryBtn = document.getElementById('refreshHistoryBtn');
        if (refreshHistoryBtn) {
            refreshHistoryBtn.addEventListener('click', () => {
                this.loadHistoryData();
            });
        }
    }

    async loadHistoryData() {
        try {
            const reliefTimetables = await reliefManager.getAllReliefTimetables();
            const historyContainer = document.getElementById('historyContainer');
            
            const historyDisplay = reliefManager.renderReliefHistory(reliefTimetables);
            historyContainer.innerHTML = '';
            historyContainer.appendChild(historyDisplay);
        } catch (error) {
            console.error('Error loading history data:', error);
            document.getElementById('historyContainer').innerHTML = '<p class="error">Failed to load relief history</p>';
        }
    }

    async viewTodayRelief() {
        const today = Utils.getToday();
        const todayRelief = await reliefManager.getReliefTimetableByDate(today);
        
        if (todayRelief) {
            this.switchTab('relief');
        }
    }

    initializeSupportModal() {
        const supportBtn = document.getElementById('supportBtn');
        const supportModal = document.getElementById('supportModal');
        const closeBtn = supportModal?.querySelector('.close');

        if (supportBtn && supportModal) {
            supportBtn.addEventListener('click', () => {
                supportModal.style.display = 'block';
            });

            if (closeBtn) {
                closeBtn.addEventListener('click', () => {
                    supportModal.style.display = 'none';
                });
            }

            window.addEventListener('click', (event) => {
                if (event.target === supportModal) {
                    supportModal.style.display = 'none';
                }
            });
        }
    }

    // Utility Methods
    showModal(modalHtml, onShow = null) {
        const dynamicModals = document.getElementById('dynamicModals');
        dynamicModals.innerHTML = modalHtml;
        
        const modal = dynamicModals.querySelector('.modal');
        const closeBtn = modal.querySelector('.close');
        
        if (closeBtn) {
            closeBtn.addEventListener('click', () => {
                modal.style.display = 'none';
            });
        }

        window.addEventListener('click', (event) => {
            if (event.target === modal) {
                modal.style.display = 'none';
            }
        });

        modal.style.display = 'block';
        
        if (onShow) {
            onShow();
        }
    }

    closeModal(modalId) {
        const modal = document.getElementById(modalId);
        if (modal) {
            modal.style.display = 'none';
        }
    }
}

// Global user dashboard instance
const userDashboard = new UserDashboard();

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', async () => {
    await userDashboard.init();
});
