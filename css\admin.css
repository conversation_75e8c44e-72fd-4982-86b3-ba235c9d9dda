/* Admin Dashboard Specific Styles */

.admin-container {
    min-height: 100vh;
    display: flex;
    flex-direction: column;
    background: #f5f5f5;
}

/* Admin <PERSON>er */
.admin-header {
    background: white;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    padding: 1rem 2rem;
}

.header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    max-width: 1200px;
    margin: 0 auto;
}

.header-content h1 {
    color: #2196F3;
    margin: 0;
    font-size: 1.8rem;
}

.user-info {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.user-info span {
    color: #666;
    font-weight: 500;
}

/* Navigation */
.admin-nav {
    background: white;
    border-bottom: 1px solid #ddd;
    padding: 0 2rem;
}

.nav-tabs {
    list-style: none;
    display: flex;
    gap: 0;
    margin: 0;
    padding: 0;
    max-width: 1200px;
    margin: 0 auto;
}

.nav-tabs li {
    margin: 0;
}

.nav-link {
    display: block;
    padding: 1rem 1.5rem;
    text-decoration: none;
    color: #666;
    border-bottom: 3px solid transparent;
    transition: all 0.3s ease;
    font-weight: 500;
}

.nav-link:hover {
    color: #2196F3;
    background: #f8f9fa;
}

.nav-link.active {
    color: #2196F3;
    border-bottom-color: #2196F3;
    background: #f8f9fa;
}

/* Main Content */
.admin-main {
    flex: 1;
    padding: 2rem;
    max-width: 1200px;
    margin: 0 auto;
    width: 100%;
}

/* Tab Content */
.tab-content {
    display: none;
}

.tab-content.active {
    display: block;
}

/* Dashboard Grid */
.dashboard-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
    margin-bottom: 2rem;
}

.dashboard-card {
    background: white;
    padding: 2rem;
    border-radius: 10px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.dashboard-card h3 {
    margin: 0 0 1rem 0;
    color: #333;
    font-size: 1.2rem;
}

.dashboard-card p {
    margin: 0.5rem 0;
    color: #666;
}

.dashboard-card span {
    font-weight: bold;
    color: #2196F3;
}

.quick-actions {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.quick-actions .btn {
    width: 100%;
}

/* Section Headers */
.section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
    padding-bottom: 1rem;
    border-bottom: 2px solid #eee;
}

.section-header h2 {
    margin: 0;
    color: #333;
}

/* Data Tables */
.data-table {
    background: white;
    border-radius: 10px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    overflow: hidden;
}

.table {
    width: 100%;
    border-collapse: collapse;
    margin: 0;
}

.table th,
.table td {
    padding: 1rem;
    text-align: left;
    border-bottom: 1px solid #eee;
}

.table th {
    background: #f8f9fa;
    font-weight: 600;
    color: #333;
}

.table tbody tr:hover {
    background: #f8f9fa;
}

/* Timetable Styles */
.timetable-controls {
    display: flex;
    gap: 1rem;
    align-items: center;
}

.timetable-controls select {
    padding: 0.5rem;
    border: 1px solid #ddd;
    border-radius: 5px;
    min-width: 200px;
}

.timetable-grid {
    background: white;
    border-radius: 10px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    overflow: hidden;
    margin-top: 1rem;
}

.timetable-table {
    width: 100%;
    border-collapse: collapse;
}

.timetable-table th,
.timetable-table td {
    padding: 0.75rem;
    text-align: center;
    border: 1px solid #ddd;
    min-width: 100px;
}

.timetable-table th {
    background: #2196F3;
    color: white;
    font-weight: 600;
}

.timetable-table .period-time {
    background: #f8f9fa;
    font-weight: 500;
    color: #666;
}

.timetable-table .interval {
    background: #fff3cd;
    color: #856404;
    font-weight: 500;
}

.timetable-table .free-period {
    background: #d4edda;
    color: #155724;
}

.timetable-table .relief-period {
    background: #f8d7da;
    color: #721c24;
}

/* Relief Controls */
.relief-controls {
    display: flex;
    gap: 1rem;
    align-items: center;
}

.relief-controls input[type="date"] {
    padding: 0.5rem;
    border: 1px solid #ddd;
    border-radius: 5px;
}

/* Backup Section */
.backup-section {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
}

.backup-card {
    background: white;
    padding: 2rem;
    border-radius: 10px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    text-align: center;
}

.backup-card h3 {
    margin: 0 0 1rem 0;
    color: #333;
}

.backup-card p {
    color: #666;
    margin-bottom: 1.5rem;
}

/* Form Controls */
.form-control {
    width: 100%;
    padding: 0.75rem;
    border: 1px solid #ddd;
    border-radius: 5px;
    font-size: 1rem;
    margin-bottom: 1rem;
}

.form-control:focus {
    outline: none;
    border-color: #2196F3;
    box-shadow: 0 0 0 2px rgba(33, 150, 243, 0.2);
}

.form-row {
    display: flex;
    gap: 1rem;
    margin-bottom: 1rem;
}

.form-row .form-control {
    margin-bottom: 0;
}

/* Action Buttons */
.action-buttons {
    display: flex;
    gap: 0.5rem;
}

.btn-sm {
    padding: 0.25rem 0.5rem;
    font-size: 0.875rem;
}

/* Loading States */
.loading {
    opacity: 0.6;
    pointer-events: none;
}

.spinner {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid #f3f3f3;
    border-top: 3px solid #2196F3;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Timetable Input Styles */
.timetable-input {
    width: 100%;
    padding: 0.25rem;
    border: 1px solid #ddd;
    border-radius: 3px;
    font-size: 0.875rem;
    text-align: center;
}

.timetable-input:focus {
    outline: none;
    border-color: #2196F3;
    box-shadow: 0 0 0 2px rgba(33, 150, 243, 0.2);
}

/* Status Indicators */
.status {
    padding: 0.25rem 0.5rem;
    border-radius: 12px;
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
}

.status.active {
    background: #d4edda;
    color: #155724;
}

.status.inactive {
    background: #f8d7da;
    color: #721c24;
}

/* Relief Timetable Specific Styles */
.relief-assignment {
    background: #fff3cd;
    border: 1px solid #ffeaa7;
    border-radius: 5px;
    padding: 0.5rem;
    margin: 0.25rem 0;
    font-size: 0.875rem;
}

.unassigned-relief {
    background: #f8d7da;
    border: 1px solid #f5c6cb;
    border-radius: 5px;
    padding: 0.5rem;
    margin: 0.25rem 0;
    font-size: 0.875rem;
}

.recent-relief-item {
    padding: 0.5rem;
    border-left: 3px solid #2196F3;
    margin-bottom: 0.5rem;
    background: #f8f9fa;
}

/* No Data Message */
.no-data {
    text-align: center;
    color: #666;
    font-style: italic;
    padding: 2rem;
    background: #f8f9fa;
    border-radius: 5px;
}

.error {
    text-align: center;
    color: #721c24;
    background: #f8d7da;
    padding: 1rem;
    border-radius: 5px;
    border: 1px solid #f5c6cb;
}

/* Teacher Checkboxes */
.teacher-checkboxes label {
    cursor: pointer;
    padding: 0.5rem;
    border-radius: 5px;
    transition: background-color 0.2s ease;
}

.teacher-checkboxes label:hover {
    background: #f8f9fa;
}

.teacher-checkboxes input[type="checkbox"] {
    margin-right: 0.5rem;
}

/* Assignment Display */
.assignment {
    background: #e3f2fd;
    border: 1px solid #bbdefb;
    border-radius: 3px;
    padding: 0.25rem;
    margin: 0.125rem 0;
    font-size: 0.75rem;
}

/* Relief Actions */
.relief-actions {
    margin-top: 1rem;
    text-align: center;
}

.relief-actions .btn {
    margin: 0 0.5rem;
}

/* Modal Enhancements */
.centered-modal {
    margin: 10% auto !important;
    max-width: 500px !important;
    width: 90% !important;
}

.centered-timetable-modal {
    margin: 5% auto !important;
    max-width: 95% !important;
    width: 1200px !important;
    max-height: 85vh !important;
    overflow-y: auto !important;
}

.timetable-editor-container {
    display: flex;
    justify-content: center;
    overflow-x: auto;
    padding: 1rem 0;
}

/* Form Enhancements */
.form-group small {
    display: block;
    color: #666;
    font-size: 0.875rem;
    margin-top: 0.25rem;
}

/* Responsive Design */
@media (max-width: 768px) {
    .admin-header {
        padding: 1rem;
    }

    .header-content {
        flex-direction: column;
        gap: 1rem;
        text-align: center;
    }

    .admin-nav {
        padding: 0 1rem;
    }

    .nav-tabs {
        flex-wrap: wrap;
        justify-content: center;
    }

    .nav-link {
        padding: 0.75rem 1rem;
        font-size: 0.9rem;
    }

    .admin-main {
        padding: 1rem;
    }

    .section-header {
        flex-direction: column;
        gap: 1rem;
        align-items: stretch;
    }

    .timetable-controls,
    .relief-controls {
        flex-direction: column;
        align-items: stretch;
    }

    .dashboard-grid {
        grid-template-columns: 1fr;
    }

    .backup-section {
        grid-template-columns: 1fr;
    }

    .form-row {
        flex-direction: column;
    }

    .action-buttons {
        justify-content: center;
    }

    .timetable-table {
        font-size: 0.75rem;
    }

    .timetable-table th,
    .timetable-table td {
        padding: 0.5rem 0.25rem;
        min-width: 80px;
    }

    .relief-actions .btn {
        display: block;
        width: 100%;
        margin: 0.5rem 0;
    }

    .centered-modal {
        margin: 5% auto !important;
        max-width: 95% !important;
        width: 95% !important;
    }

    .centered-timetable-modal {
        margin: 2% auto !important;
        max-width: 98% !important;
        width: 98% !important;
        max-height: 95vh !important;
    }

    .timetable-editor-container {
        padding: 0.5rem 0;
    }
}
