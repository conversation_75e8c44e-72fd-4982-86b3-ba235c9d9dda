// Timetable management module
class TimetableManager {
    constructor() {
        this.periodTimes = Utils.getPeriodTimes();
        this.daysOfWeek = Utils.getDaysOfWeek();
    }

    async createTeacher(teacherData) {
        try {
            // Validate input
            if (!teacherData.name || !teacherData.subject) {
                return { success: false, message: 'Name and subject are required' };
            }

            // Check if teacher already exists
            const existingTeachers = await db.getAll('teachers');
            const duplicate = existingTeachers.find(t => 
                t.name.toLowerCase() === teacherData.name.toLowerCase()
            );
            
            if (duplicate) {
                return { success: false, message: 'Teacher with this name already exists' };
            }

            const teacher = {
                name: Utils.sanitizeInput(teacherData.name),
                subject: Utils.sanitizeInput(teacherData.subject),
                timetable: teacherData.timetable || Utils.createEmptyTimetable(),
                isActive: true
            };

            const teacherId = await db.add('teachers', teacher);
            return { success: true, teacherId, message: 'Teacher created successfully' };
        } catch (error) {
            console.error('Error creating teacher:', error);
            return { success: false, message: 'Failed to create teacher' };
        }
    }

    async updateTeacher(teacherId, teacherData) {
        try {
            const teacher = await db.get('teachers', teacherId);
            if (!teacher) {
                return { success: false, message: 'Teacher not found' };
            }

            const updatedTeacher = {
                ...teacher,
                name: Utils.sanitizeInput(teacherData.name) || teacher.name,
                subject: Utils.sanitizeInput(teacherData.subject) || teacher.subject,
                timetable: teacherData.timetable || teacher.timetable,
                isActive: teacherData.isActive !== undefined ? teacherData.isActive : teacher.isActive
            };

            await db.update('teachers', updatedTeacher);
            return { success: true, message: 'Teacher updated successfully' };
        } catch (error) {
            console.error('Error updating teacher:', error);
            return { success: false, message: 'Failed to update teacher' };
        }
    }

    async deleteTeacher(teacherId) {
        try {
            await db.delete('teachers', teacherId);
            return { success: true, message: 'Teacher deleted successfully' };
        } catch (error) {
            console.error('Error deleting teacher:', error);
            return { success: false, message: 'Failed to delete teacher' };
        }
    }

    async getAllTeachers() {
        try {
            return await db.getAll('teachers');
        } catch (error) {
            console.error('Error getting teachers:', error);
            return [];
        }
    }

    async getTeacher(teacherId) {
        try {
            return await db.get('teachers', teacherId);
        } catch (error) {
            console.error('Error getting teacher:', error);
            return null;
        }
    }

    async createSubject(subjectData) {
        try {
            if (!subjectData.name) {
                return { success: false, message: 'Subject name is required' };
            }

            // Check if subject already exists
            const existingSubject = await db.getByIndex('subjects', 'name', subjectData.name);
            if (existingSubject) {
                return { success: false, message: 'Subject already exists' };
            }

            const subject = {
                name: Utils.sanitizeInput(subjectData.name),
                code: subjectData.code || '',
                description: subjectData.description || ''
            };

            const subjectId = await db.add('subjects', subject);
            return { success: true, subjectId, message: 'Subject created successfully' };
        } catch (error) {
            console.error('Error creating subject:', error);
            return { success: false, message: 'Failed to create subject' };
        }
    }

    async updateSubject(subjectId, subjectData) {
        try {
            const subject = await db.get('subjects', subjectId);
            if (!subject) {
                return { success: false, message: 'Subject not found' };
            }

            const updatedSubject = {
                ...subject,
                name: Utils.sanitizeInput(subjectData.name) || subject.name,
                code: Utils.sanitizeInput(subjectData.code) || subject.code,
                description: Utils.sanitizeInput(subjectData.description) || subject.description
            };

            await db.update('subjects', updatedSubject);
            return { success: true, message: 'Subject updated successfully' };
        } catch (error) {
            console.error('Error updating subject:', error);
            return { success: false, message: 'Failed to update subject' };
        }
    }

    async deleteSubject(subjectId) {
        try {
            await db.delete('subjects', subjectId);
            return { success: true, message: 'Subject deleted successfully' };
        } catch (error) {
            console.error('Error deleting subject:', error);
            return { success: false, message: 'Failed to delete subject' };
        }
    }

    async getAllSubjects() {
        try {
            return await db.getAll('subjects');
        } catch (error) {
            console.error('Error getting subjects:', error);
            return [];
        }
    }

    async updateTeacherTimetable(teacherId, timetable) {
        try {
            if (!Utils.validateTimetable(timetable)) {
                return { success: false, message: 'Invalid timetable format' };
            }

            const teacher = await db.get('teachers', teacherId);
            if (!teacher) {
                return { success: false, message: 'Teacher not found' };
            }

            teacher.timetable = timetable;
            await db.update('teachers', teacher);
            
            return { success: true, message: 'Timetable updated successfully' };
        } catch (error) {
            console.error('Error updating timetable:', error);
            return { success: false, message: 'Failed to update timetable' };
        }
    }

    async generateMasterTimetable() {
        try {
            const teachers = await this.getAllTeachers();
            const masterTimetable = {};

            // Initialize master timetable structure
            this.daysOfWeek.forEach(day => {
                masterTimetable[day] = {};
                Object.keys(this.periodTimes).forEach(period => {
                    if (period !== 'interval') {
                        masterTimetable[day][period] = [];
                    }
                });
            });

            // Populate master timetable with teacher assignments
            teachers.forEach(teacher => {
                if (teacher.timetable && teacher.isActive) {
                    this.daysOfWeek.forEach(day => {
                        Object.keys(this.periodTimes).forEach(period => {
                            if (period !== 'interval' && teacher.timetable[day] && teacher.timetable[day][period]) {
                                const subject = teacher.timetable[day][period];
                                if (subject.trim()) {
                                    masterTimetable[day][period].push({
                                        teacher: teacher.name,
                                        subject: subject,
                                        teacherId: teacher.id
                                    });
                                }
                            }
                        });
                    });
                }
            });

            return masterTimetable;
        } catch (error) {
            console.error('Error generating master timetable:', error);
            return {};
        }
    }

    renderTimetableGrid(timetable, isEditable = false, teacherId = null) {
        const table = document.createElement('table');
        table.className = 'timetable-table';

        // Create header row
        const headerRow = document.createElement('tr');
        headerRow.innerHTML = '<th>Time</th>';
        this.daysOfWeek.forEach(day => {
            headerRow.innerHTML += `<th>${day}</th>`;
        });
        table.appendChild(headerRow);

        // Create period rows in correct order (1-5, interval, 6-8)
        const periodOrder = ['1', '2', '3', '4', '5', 'interval', '6', '7', '8'];

        periodOrder.forEach(period => {
            const time = this.periodTimes[period];

            if (period === 'interval') {
                // Interval row
                const intervalRow = document.createElement('tr');
                intervalRow.innerHTML = `
                    <td class="period-time interval">${time}</td>
                    <td colspan="5" class="interval">INTERVAL</td>
                `;
                table.appendChild(intervalRow);
            } else {
                // Regular period row
                const row = document.createElement('tr');
                row.innerHTML = `<td class="period-time">${time}</td>`;

                this.daysOfWeek.forEach(day => {
                    const cellValue = timetable[day] && timetable[day][period] ? timetable[day][period] : '';
                    const cellClass = cellValue.trim() === '' ? 'free-period' : '';

                    if (isEditable) {
                        row.innerHTML += `
                            <td class="${cellClass}">
                                <input type="text"
                                       value="${cellValue}"
                                       data-day="${day}"
                                       data-period="${period}"
                                       class="timetable-input"
                                       placeholder="Free"
                                       list="subjectsList">
                            </td>
                        `;
                    } else {
                        const displayValue = cellValue.trim() === '' ? 'Free' : cellValue;
                        row.innerHTML += `<td class="${cellClass}">${displayValue}</td>`;
                    }
                });

                table.appendChild(row);
            }
        });

        // Add datalist for subject suggestions if editable
        if (isEditable) {
            const datalist = document.createElement('datalist');
            datalist.id = 'subjectsList';
            this.loadSubjectsForDatalist(datalist);
            table.appendChild(datalist);
        }

        return table;
    }

    async loadSubjectsForDatalist(datalist) {
        try {
            const subjects = await this.getAllSubjects();
            subjects.forEach(subject => {
                const option = document.createElement('option');
                option.value = subject.name;
                datalist.appendChild(option);
            });
        } catch (error) {
            console.error('Error loading subjects for datalist:', error);
        }
    }

    renderMasterTimetable(masterTimetable) {
        const table = document.createElement('table');
        table.className = 'timetable-table';

        // Create header row
        const headerRow = document.createElement('tr');
        headerRow.innerHTML = '<th>Time</th>';
        this.daysOfWeek.forEach(day => {
            headerRow.innerHTML += `<th>${day}</th>`;
        });
        table.appendChild(headerRow);

        // Create period rows in correct order (1-5, interval, 6-8)
        const periodOrder = ['1', '2', '3', '4', '5', 'interval', '6', '7', '8'];

        periodOrder.forEach(period => {
            const time = this.periodTimes[period];

            if (period === 'interval') {
                // Interval row
                const intervalRow = document.createElement('tr');
                intervalRow.innerHTML = `
                    <td class="period-time interval">${time}</td>
                    <td colspan="5" class="interval">INTERVAL</td>
                `;
                table.appendChild(intervalRow);
            } else {
                // Regular period row
                const row = document.createElement('tr');
                row.innerHTML = `<td class="period-time">${time}</td>`;

                this.daysOfWeek.forEach(day => {
                    const assignments = masterTimetable[day] && masterTimetable[day][period] ? masterTimetable[day][period] : [];
                    let cellContent = '';

                    if (assignments.length === 0) {
                        cellContent = '<span class="free-period">Free</span>';
                    } else {
                        cellContent = assignments.map(assignment =>
                            `<div class="assignment">${assignment.teacher} - ${assignment.subject}</div>`
                        ).join('');
                    }

                    row.innerHTML += `<td>${cellContent}</td>`;
                });

                table.appendChild(row);
            }
        });

        return table;
    }

    extractTimetableFromForm(container) {
        const timetable = Utils.createEmptyTimetable();
        const inputs = container.querySelectorAll('.timetable-input');
        
        inputs.forEach(input => {
            const day = input.dataset.day;
            const period = input.dataset.period;
            const value = input.value.trim();
            
            if (timetable[day] && timetable[day][period] !== undefined) {
                timetable[day][period] = value;
            }
        });
        
        return timetable;
    }

    checkTimetableConflicts(timetable, teacherId = null) {
        // This method can be extended to check for conflicts
        // For now, it's a placeholder for future conflict detection
        const conflicts = [];
        
        // Example: Check if a teacher is assigned to multiple subjects at the same time
        // This would require more complex logic based on your specific requirements
        
        return conflicts;
    }
}

// Global timetable manager instance
const timetableManager = new TimetableManager();
