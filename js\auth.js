// Authentication module
class Auth {
    constructor() {
        this.currentUser = null;
        this.defaultAdminPassword = 'admin123'; // Default admin password
    }

    async init() {
        // Check if admin user exists, if not create one
        const adminUser = await db.getByIndex('users', 'username', 'admin');
        if (!adminUser) {
            await this.createDefaultAdmin();
        }

        // Check for existing session
        const savedUser = localStorage.getItem('currentUser');
        if (savedUser) {
            this.currentUser = JSON.parse(savedUser);
        }
    }

    async createDefaultAdmin() {
        try {
            await db.add('users', {
                username: 'admin',
                password: this.hashPassword(this.defaultAdminPassword),
                role: 'admin',
                fullName: 'System Administrator'
            });
            console.log('Default admin user created');
        } catch (error) {
            console.error('Error creating default admin:', error);
        }
    }

    hashPassword(password) {
        // Simple hash function for demo purposes
        // In production, use a proper hashing library like bcrypt
        let hash = 0;
        for (let i = 0; i < password.length; i++) {
            const char = password.charCodeAt(i);
            hash = ((hash << 5) - hash) + char;
            hash = hash & hash; // Convert to 32-bit integer
        }
        return hash.toString();
    }

    async login(username, password, userType) {
        try {
            if (userType === 'admin') {
                // Admin login
                if (username === 'admin' && password === this.defaultAdminPassword) {
                    this.currentUser = {
                        id: 1,
                        username: 'admin',
                        role: 'admin',
                        fullName: 'System Administrator'
                    };
                    this.saveSession();
                    return { success: true, user: this.currentUser };
                } else {
                    return { success: false, message: 'Invalid admin credentials' };
                }
            } else {
                // Regular user login
                const user = await db.getByIndex('users', 'username', username);
                if (user && user.password === this.hashPassword(password) && user.role === 'user') {
                    this.currentUser = {
                        id: user.id,
                        username: user.username,
                        role: user.role,
                        fullName: user.fullName
                    };
                    this.saveSession();
                    return { success: true, user: this.currentUser };
                } else {
                    return { success: false, message: 'Invalid user credentials' };
                }
            }
        } catch (error) {
            console.error('Login error:', error);
            return { success: false, message: 'Login failed. Please try again.' };
        }
    }

    async createUser(userData) {
        try {
            // Check if username already exists
            const existingUser = await db.getByIndex('users', 'username', userData.username);
            if (existingUser) {
                return { success: false, message: 'Username already exists' };
            }

            const newUser = {
                username: userData.username,
                password: this.hashPassword(userData.password),
                role: 'user',
                fullName: userData.fullName || userData.username
            };

            const userId = await db.add('users', newUser);
            return { success: true, userId, message: 'User created successfully' };
        } catch (error) {
            console.error('Error creating user:', error);
            return { success: false, message: 'Failed to create user' };
        }
    }

    async getAllUsers() {
        try {
            const users = await db.getAllByIndex('users', 'role', 'user');
            return users.map(user => ({
                id: user.id,
                username: user.username,
                fullName: user.fullName,
                createdAt: user.createdAt
            }));
        } catch (error) {
            console.error('Error getting users:', error);
            return [];
        }
    }

    async deleteUser(userId) {
        try {
            await db.delete('users', userId);
            return { success: true, message: 'User deleted successfully' };
        } catch (error) {
            console.error('Error deleting user:', error);
            return { success: false, message: 'Failed to delete user' };
        }
    }

    async updateUser(userId, userData) {
        try {
            const user = await db.get('users', userId);
            if (!user) {
                return { success: false, message: 'User not found' };
            }

            const updatedUser = {
                ...user,
                fullName: userData.fullName || user.fullName
            };

            if (userData.password) {
                updatedUser.password = this.hashPassword(userData.password);
            }

            await db.update('users', updatedUser);
            return { success: true, message: 'User updated successfully' };
        } catch (error) {
            console.error('Error updating user:', error);
            return { success: false, message: 'Failed to update user' };
        }
    }

    saveSession() {
        localStorage.setItem('currentUser', JSON.stringify(this.currentUser));
    }

    logout() {
        this.currentUser = null;
        localStorage.removeItem('currentUser');
        window.location.href = 'index.html';
    }

    isLoggedIn() {
        return this.currentUser !== null;
    }

    isAdmin() {
        return this.currentUser && this.currentUser.role === 'admin';
    }

    getCurrentUser() {
        return this.currentUser;
    }

    requireAuth() {
        if (!this.isLoggedIn()) {
            window.location.href = 'index.html';
            return false;
        }
        return true;
    }

    requireAdmin() {
        if (!this.isLoggedIn() || !this.isAdmin()) {
            window.location.href = 'index.html';
            return false;
        }
        return true;
    }
}

// Global auth instance
const auth = new Auth();
