{"version": 3, "file": "upload.js", "sourceRoot": "", "sources": ["../../src/gridfs/upload.ts"], "names": [], "mappings": ";;;AAAA,mCAAkC;AAGlC,kCAAmC;AAEnC,oCAAyF;AAGzF,sDAAkD;AA0BlD;;;;;GAKG;AACH,MAAa,uBAAwB,SAAQ,iBAAQ;IA+BnD;;;;;OAKG;IACH,YAAY,MAAoB,EAAE,QAAgB,EAAE,OAAwC;QAC1F,KAAK,EAAE,CAAC;QAER,OAAO,GAAG,OAAO,IAAI,EAAE,CAAC;QACxB,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;QACrB,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC,CAAC,CAAC,iBAAiB,CAAC;QACzC,IAAI,CAAC,QAAQ,GAAG,QAAQ,CAAC;QACzB,IAAI,CAAC,KAAK,GAAG,MAAM,CAAC,CAAC,CAAC,gBAAgB,CAAC;QACvC,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC;QACvB,IAAI,CAAC,YAAY,GAAG,4BAAY,CAAC,WAAW,CAAC,OAAO,CAAC,IAAI,MAAM,CAAC,CAAC,CAAC,OAAO,CAAC,YAAY,CAAC;QACvF,gCAAgC;QAChC,IAAI,CAAC,IAAI,GAAG,KAAK,CAAC;QAElB,IAAI,CAAC,EAAE,GAAG,OAAO,CAAC,EAAE,CAAC,CAAC,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,eAAQ,EAAE,CAAC;QACnD,qDAAqD;QACrD,IAAI,CAAC,cAAc,GAAG,OAAO,CAAC,cAAc,IAAI,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,OAAO,CAAC,cAAc,CAAC;QACrF,IAAI,CAAC,UAAU,GAAG,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;QACpD,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC;QAChB,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC;QACX,IAAI,CAAC,GAAG,GAAG,CAAC,CAAC;QACb,IAAI,CAAC,KAAK,GAAG;YACX,SAAS,EAAE,KAAK;YAChB,mBAAmB,EAAE,CAAC;YACtB,OAAO,EAAE,KAAK;YACd,OAAO,EAAE,KAAK;SACf,CAAC;QAEF,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,sBAAsB,EAAE;YACzC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,sBAAsB,GAAG,IAAI,CAAC;YAE5C,YAAY,CAAC,IAAI,CAAC,CAAC,IAAI,CACrB,GAAG,EAAE;gBACH,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,cAAc,GAAG,IAAI,CAAC;gBACpC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;YAC5B,CAAC,EACD,GAAG,EAAE,CAAC,IAAI,CACX,CAAC;SACH;IACH,CAAC;IAkBQ,KAAK,CACZ,KAAsB,EACtB,kBAAoD,EACpD,QAAyB;QAEzB,MAAM,QAAQ,GAAG,OAAO,kBAAkB,KAAK,UAAU,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,kBAAkB,CAAC;QAC3F,QAAQ,GAAG,OAAO,kBAAkB,KAAK,UAAU,CAAC,CAAC,CAAC,kBAAkB,CAAC,CAAC,CAAC,QAAQ,CAAC;QACpF,OAAO,cAAc,CAAC,IAAI,EAAE,GAAG,EAAE,CAAC,OAAO,CAAC,IAAI,EAAE,KAAK,EAAE,QAAQ,EAAE,QAAQ,CAAC,CAAC,CAAC;IAC9E,CAAC;IAED;;;OAGG;IACH,KAAK,CAAC,KAAK;QACT,IAAI,IAAI,CAAC,KAAK,CAAC,SAAS,EAAE;YACxB,wDAAwD;YACxD,MAAM,IAAI,qBAAa,CAAC,kDAAkD,CAAC,CAAC;SAC7E;QAED,IAAI,IAAI,CAAC,KAAK,CAAC,OAAO,EAAE;YACtB,wDAAwD;YACxD,MAAM,IAAI,qBAAa,CAAC,uCAAuC,CAAC,CAAC;SAClE;QAED,IAAI,CAAC,KAAK,CAAC,OAAO,GAAG,IAAI,CAAC;QAC1B,MAAM,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,EAAE,QAAQ,EAAE,IAAI,CAAC,EAAE,EAAE,CAAC,CAAC;IACtD,CAAC;IAqBQ,GAAG,CACV,eAAsD,EACtD,kBAAiE,EACjE,QAAsC;QAEtC,MAAM,KAAK,GAAG,OAAO,eAAe,KAAK,UAAU,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,eAAe,CAAC;QAClF,MAAM,QAAQ,GAAG,OAAO,kBAAkB,KAAK,UAAU,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,kBAAkB,CAAC;QAC3F,QAAQ;YACN,OAAO,eAAe,KAAK,UAAU;gBACnC,CAAC,CAAC,eAAe;gBACjB,CAAC,CAAC,OAAO,kBAAkB,KAAK,UAAU;oBAC1C,CAAC,CAAC,kBAAkB;oBACpB,CAAC,CAAC,QAAQ,CAAC;QAEf,IAAI,IAAI,CAAC,KAAK,CAAC,SAAS,IAAI,YAAY,CAAC,IAAI,EAAE,QAAQ,CAAC;YAAE,OAAO,IAAI,CAAC;QAEtE,IAAI,CAAC,KAAK,CAAC,SAAS,GAAG,IAAI,CAAC;QAE5B,IAAI,QAAQ,EAAE;YACZ,IAAI,CAAC,IAAI,CAAC,uBAAuB,CAAC,MAAM,EAAE,CAAC,MAAkB,EAAE,EAAE;gBAC/D,IAAI,QAAQ;oBAAE,QAAQ,CAAC,SAAS,EAAE,MAAM,CAAC,CAAC;YAC5C,CAAC,CAAC,CAAC;SACJ;QAED,IAAI,CAAC,KAAK,EAAE;YACV,cAAc,CAAC,IAAI,EAAE,GAAG,EAAE,CAAC,CAAC,CAAC,YAAY,CAAC,IAAI,CAAC,CAAC,CAAC;YACjD,OAAO,IAAI,CAAC;SACb;QAED,IAAI,CAAC,KAAK,CAAC,KAAK,EAAE,QAAQ,EAAE,GAAG,EAAE;YAC/B,YAAY,CAAC,IAAI,CAAC,CAAC;QACrB,CAAC,CAAC,CAAC;QAEH,OAAO,IAAI,CAAC;IACd,CAAC;;AA1JD,aAAa;AACG,6BAAK,GAAG,OAAO,CAAC;AAChC,aAAa;AACG,6BAAK,GAAG,OAAO,CAAC;AAChC;;;GAGG;AACa,8BAAM,GAAG,QAAQ,CAAC;AA7BvB,0DAAuB;AAkLpC,SAAS,aAAa,CACpB,MAA+B,EAC/B,KAAe,EACf,QAAmB;IAEnB,IAAI,MAAM,CAAC,KAAK,CAAC,OAAO,EAAE;QACxB,OAAO;KACR;IACD,MAAM,CAAC,KAAK,CAAC,OAAO,GAAG,IAAI,CAAC;IAC5B,IAAI,QAAQ,EAAE;QACZ,OAAO,QAAQ,CAAC,KAAK,CAAC,CAAC;KACxB;IACD,MAAM,CAAC,IAAI,CAAC,uBAAuB,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC;AACpD,CAAC;AAED,SAAS,cAAc,CAAC,OAAiB,EAAE,CAAS,EAAE,IAAY;IAChE,OAAO;QACL,GAAG,EAAE,IAAI,eAAQ,EAAE;QACnB,QAAQ,EAAE,OAAO;QACjB,CAAC;QACD,IAAI;KACL,CAAC;AACJ,CAAC;AAED,KAAK,UAAU,gBAAgB,CAAC,MAA+B;IAC7D,MAAM,KAAK,GAAG,EAAE,QAAQ,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC;IAEpC,IAAI,OAAO,CAAC;IACZ,IAAI;QACF,OAAO,GAAG,MAAM,MAAM,CAAC,MAAM,CAAC,WAAW,EAAE,CAAC,OAAO,EAAE,CAAC;KACvD;IAAC,OAAO,KAAK,EAAE;QACd,IAAI,KAAK,YAAY,kBAAU,IAAI,KAAK,CAAC,IAAI,KAAK,2BAAmB,CAAC,iBAAiB,EAAE;YACvF,OAAO,GAAG,EAAE,CAAC;SACd;aAAM;YACL,MAAM,KAAK,CAAC;SACb;KACF;IAED,MAAM,cAAc,GAAG,CAAC,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE;QAC5C,MAAM,IAAI,GAAG,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;QACpC,IAAI,IAAI,CAAC,MAAM,KAAK,CAAC,IAAI,KAAK,CAAC,GAAG,CAAC,QAAQ,KAAK,CAAC,IAAI,KAAK,CAAC,GAAG,CAAC,CAAC,KAAK,CAAC,EAAE;YACtE,OAAO,IAAI,CAAC;SACb;QACD,OAAO,KAAK,CAAC;IACf,CAAC,CAAC,CAAC;IAEH,IAAI,CAAC,cAAc,EAAE;QACnB,MAAM,MAAM,CAAC,MAAM,CAAC,WAAW,CAAC,KAAK,EAAE;YACrC,GAAG,MAAM,CAAC,YAAY;YACtB,UAAU,EAAE,IAAI;YAChB,MAAM,EAAE,IAAI;SACb,CAAC,CAAC;KACJ;AACH,CAAC;AAED,SAAS,SAAS,CAAC,MAA+B,EAAE,QAAmB;IACrE,IAAI,MAAM,CAAC,IAAI;QAAE,OAAO,IAAI,CAAC;IAC7B,IAAI,MAAM,CAAC,KAAK,CAAC,SAAS,IAAI,MAAM,CAAC,KAAK,CAAC,mBAAmB,KAAK,CAAC,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,OAAO,EAAE;QAC7F,yDAAyD;QACzD,MAAM,CAAC,IAAI,GAAG,IAAI,CAAC;QACnB,yBAAyB;QACzB,MAAM,QAAQ,GAAG,cAAc,CAC7B,MAAM,CAAC,EAAE,EACT,MAAM,CAAC,MAAM,EACb,MAAM,CAAC,cAAc,EACrB,MAAM,CAAC,QAAQ,EACf,MAAM,CAAC,OAAO,CAAC,WAAW,EAC1B,MAAM,CAAC,OAAO,CAAC,OAAO,EACtB,MAAM,CAAC,OAAO,CAAC,QAAQ,CACxB,CAAC;QAEF,IAAI,YAAY,CAAC,MAAM,EAAE,QAAQ,CAAC,EAAE;YAClC,OAAO,KAAK,CAAC;SACd;QAED,MAAM,CAAC,KAAK,CAAC,SAAS,CAAC,QAAQ,EAAE,EAAE,YAAY,EAAE,MAAM,CAAC,YAAY,EAAE,CAAC,CAAC,IAAI,CAC1E,GAAG,EAAE;YACH,MAAM,CAAC,IAAI,CAAC,uBAAuB,CAAC,MAAM,EAAE,QAAQ,CAAC,CAAC;YACtD,MAAM,CAAC,IAAI,CAAC,uBAAuB,CAAC,KAAK,CAAC,CAAC;QAC7C,CAAC,EACD,KAAK,CAAC,EAAE;YACN,OAAO,aAAa,CAAC,MAAM,EAAE,KAAK,EAAE,QAAQ,CAAC,CAAC;QAChD,CAAC,CACF,CAAC;QAEF,OAAO,IAAI,CAAC;KACb;IAED,OAAO,KAAK,CAAC;AACf,CAAC;AAED,KAAK,UAAU,YAAY,CAAC,MAA+B;IACzD,MAAM,GAAG,GAAG,MAAM,MAAM,CAAC,KAAK,CAAC,OAAO,CAAC,EAAE,EAAE,EAAE,UAAU,EAAE,EAAE,GAAG,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC;IACvE,IAAI,GAAG,IAAI,IAAI,EAAE;QACf,+EAA+E;QAC/E,OAAO;KACR;IAED,MAAM,KAAK,GAAG,EAAE,QAAQ,EAAE,CAAC,EAAE,UAAU,EAAE,CAAC,EAAE,CAAC;IAE7C,IAAI,OAAO,CAAC;IACZ,IAAI;QACF,OAAO,GAAG,MAAM,MAAM,CAAC,KAAK,CAAC,WAAW,EAAE,CAAC,OAAO,EAAE,CAAC;KACtD;IAAC,OAAO,KAAK,EAAE;QACd,IAAI,KAAK,YAAY,kBAAU,IAAI,KAAK,CAAC,IAAI,KAAK,2BAAmB,CAAC,iBAAiB,EAAE;YACvF,OAAO,GAAG,EAAE,CAAC;SACd;aAAM;YACL,MAAM,KAAK,CAAC;SACb;KACF;IAED,MAAM,YAAY,GAAG,CAAC,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE;QAC1C,MAAM,IAAI,GAAG,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;QACpC,IAAI,IAAI,CAAC,MAAM,KAAK,CAAC,IAAI,KAAK,CAAC,GAAG,CAAC,QAAQ,KAAK,CAAC,IAAI,KAAK,CAAC,GAAG,CAAC,UAAU,KAAK,CAAC,EAAE;YAC/E,OAAO,IAAI,CAAC;SACb;QACD,OAAO,KAAK,CAAC;IACf,CAAC,CAAC,CAAC;IAEH,IAAI,CAAC,YAAY,EAAE;QACjB,MAAM,MAAM,CAAC,KAAK,CAAC,WAAW,CAAC,KAAK,EAAE,EAAE,UAAU,EAAE,KAAK,EAAE,CAAC,CAAC;KAC9D;IAED,MAAM,gBAAgB,CAAC,MAAM,CAAC,CAAC;AACjC,CAAC;AAED,SAAS,cAAc,CACrB,GAAa,EACb,MAAc,EACd,SAAiB,EACjB,QAAgB,EAChB,WAAoB,EACpB,OAAkB,EAClB,QAAmB;IAEnB,MAAM,GAAG,GAAe;QACtB,GAAG;QACH,MAAM;QACN,SAAS;QACT,UAAU,EAAE,IAAI,IAAI,EAAE;QACtB,QAAQ;KACT,CAAC;IAEF,IAAI,WAAW,EAAE;QACf,GAAG,CAAC,WAAW,GAAG,WAAW,CAAC;KAC/B;IAED,IAAI,OAAO,EAAE;QACX,GAAG,CAAC,OAAO,GAAG,OAAO,CAAC;KACvB;IAED,IAAI,QAAQ,EAAE;QACZ,GAAG,CAAC,QAAQ,GAAG,QAAQ,CAAC;KACzB;IAED,OAAO,GAAG,CAAC;AACb,CAAC;AAED,SAAS,OAAO,CACd,MAA+B,EAC/B,KAAsB,EACtB,QAAyB,EACzB,QAAyB;IAEzB,IAAI,YAAY,CAAC,MAAM,EAAE,QAAQ,CAAC,EAAE;QAClC,OAAO,KAAK,CAAC;KACd;IAED,MAAM,QAAQ,GAAG,MAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK,EAAE,QAAQ,CAAC,CAAC;IAE/E,MAAM,CAAC,MAAM,IAAI,QAAQ,CAAC,MAAM,CAAC;IAEjC,6CAA6C;IAC7C,IAAI,MAAM,CAAC,GAAG,GAAG,QAAQ,CAAC,MAAM,GAAG,MAAM,CAAC,cAAc,EAAE;QACxD,QAAQ,CAAC,IAAI,CAAC,MAAM,CAAC,UAAU,EAAE,MAAM,CAAC,GAAG,CAAC,CAAC;QAC7C,MAAM,CAAC,GAAG,IAAI,QAAQ,CAAC,MAAM,CAAC;QAE9B,QAAQ,IAAI,QAAQ,EAAE,CAAC;QAEvB,qEAAqE;QACrE,mDAAmD;QACnD,sCAAsC;QACtC,OAAO,IAAI,CAAC;KACb;IAED,sEAAsE;IACtE,cAAc;IACd,IAAI,iBAAiB,GAAG,QAAQ,CAAC,MAAM,CAAC;IACxC,IAAI,cAAc,GAAW,MAAM,CAAC,cAAc,GAAG,MAAM,CAAC,GAAG,CAAC;IAChE,IAAI,SAAS,GAAG,IAAI,CAAC,GAAG,CAAC,cAAc,EAAE,QAAQ,CAAC,MAAM,CAAC,CAAC;IAC1D,IAAI,mBAAmB,GAAG,CAAC,CAAC;IAC5B,OAAO,iBAAiB,GAAG,CAAC,EAAE;QAC5B,MAAM,WAAW,GAAG,QAAQ,CAAC,MAAM,GAAG,iBAAiB,CAAC;QACxD,QAAQ,CAAC,IAAI,CAAC,MAAM,CAAC,UAAU,EAAE,MAAM,CAAC,GAAG,EAAE,WAAW,EAAE,WAAW,GAAG,SAAS,CAAC,CAAC;QACnF,MAAM,CAAC,GAAG,IAAI,SAAS,CAAC;QACxB,cAAc,IAAI,SAAS,CAAC;QAC5B,IAAI,GAAgB,CAAC;QACrB,IAAI,cAAc,KAAK,CAAC,EAAE;YACxB,GAAG,GAAG,cAAc,CAAC,MAAM,CAAC,EAAE,EAAE,MAAM,CAAC,CAAC,EAAE,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC,CAAC;YAC1E,EAAE,MAAM,CAAC,KAAK,CAAC,mBAAmB,CAAC;YACnC,EAAE,mBAAmB,CAAC;YAEtB,IAAI,YAAY,CAAC,MAAM,EAAE,QAAQ,CAAC,EAAE;gBAClC,OAAO,KAAK,CAAC;aACd;YAED,MAAM,CAAC,MAAM,CAAC,SAAS,CAAC,GAAG,EAAE,EAAE,YAAY,EAAE,MAAM,CAAC,YAAY,EAAE,CAAC,CAAC,IAAI,CACtE,GAAG,EAAE;gBACH,EAAE,MAAM,CAAC,KAAK,CAAC,mBAAmB,CAAC;gBACnC,EAAE,mBAAmB,CAAC;gBAEtB,IAAI,CAAC,mBAAmB,EAAE;oBACxB,MAAM,CAAC,IAAI,CAAC,OAAO,EAAE,GAAG,CAAC,CAAC;oBAC1B,QAAQ,IAAI,QAAQ,EAAE,CAAC;oBACvB,SAAS,CAAC,MAAM,CAAC,CAAC;iBACnB;YACH,CAAC,EACD,KAAK,CAAC,EAAE;gBACN,OAAO,aAAa,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC;YACtC,CAAC,CACF,CAAC;YAEF,cAAc,GAAG,MAAM,CAAC,cAAc,CAAC;YACvC,MAAM,CAAC,GAAG,GAAG,CAAC,CAAC;YACf,EAAE,MAAM,CAAC,CAAC,CAAC;SACZ;QACD,iBAAiB,IAAI,SAAS,CAAC;QAC/B,SAAS,GAAG,IAAI,CAAC,GAAG,CAAC,cAAc,EAAE,iBAAiB,CAAC,CAAC;KACzD;IAED,qEAAqE;IACrE,mDAAmD;IACnD,4DAA4D;IAC5D,OAAO,KAAK,CAAC;AACf,CAAC;AAED,SAAS,cAAc,CACrB,MAA+B,EAC/B,QAAmC;IAEnC,IAAI,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,cAAc,EAAE;QAClC,OAAO,QAAQ,CAAC,KAAK,CAAC,CAAC;KACxB;IAED,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,OAAO,EAAE,GAAG,EAAE;QAC/B,QAAQ,CAAC,IAAI,CAAC,CAAC;IACjB,CAAC,CAAC,CAAC;IAEH,OAAO,IAAI,CAAC;AACd,CAAC;AAED,SAAS,YAAY,CAAC,MAA+B,EAAE,QAAmB;IACxE,6CAA6C;IAC7C,IAAI,MAAM,CAAC,GAAG,KAAK,CAAC,EAAE;QACpB,OAAO,SAAS,CAAC,MAAM,EAAE,QAAQ,CAAC,CAAC;KACpC;IAED,EAAE,MAAM,CAAC,KAAK,CAAC,mBAAmB,CAAC;IAEnC,yEAAyE;IACzE,SAAS;IACT,MAAM,OAAO,GAAG,MAAM,CAAC,KAAK,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;IACzC,MAAM,CAAC,UAAU,CAAC,IAAI,CAAC,OAAO,EAAE,CAAC,EAAE,CAAC,EAAE,MAAM,CAAC,GAAG,CAAC,CAAC;IAClD,MAAM,GAAG,GAAG,cAAc,CAAC,MAAM,CAAC,EAAE,EAAE,MAAM,CAAC,CAAC,EAAE,OAAO,CAAC,CAAC;IAEzD,kDAAkD;IAClD,IAAI,YAAY,CAAC,MAAM,EAAE,QAAQ,CAAC,EAAE;QAClC,OAAO,KAAK,CAAC;KACd;IAED,MAAM,CAAC,MAAM,CAAC,SAAS,CAAC,GAAG,EAAE,EAAE,YAAY,EAAE,MAAM,CAAC,YAAY,EAAE,CAAC,CAAC,IAAI,CACtE,GAAG,EAAE;QACH,EAAE,MAAM,CAAC,KAAK,CAAC,mBAAmB,CAAC;QACnC,SAAS,CAAC,MAAM,CAAC,CAAC;IACpB,CAAC,EACD,KAAK,CAAC,EAAE;QACN,OAAO,aAAa,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC;IACtC,CAAC,CACF,CAAC;IACF,OAAO,IAAI,CAAC;AACd,CAAC;AAED,SAAS,YAAY,CAAC,MAA+B,EAAE,QAAyB;IAC9E,IAAI,MAAM,CAAC,KAAK,CAAC,OAAO,EAAE;QACxB,IAAI,OAAO,QAAQ,KAAK,UAAU,EAAE;YAClC,6DAA6D;YAC7D,QAAQ,CAAC,IAAI,qBAAa,CAAC,yBAAyB,CAAC,CAAC,CAAC;SACxD;QACD,OAAO,IAAI,CAAC;KACb;IACD,OAAO,KAAK,CAAC;AACf,CAAC"}