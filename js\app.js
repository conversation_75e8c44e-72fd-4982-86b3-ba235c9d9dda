// Main application logic
class App {
    constructor() {
        this.isInitialized = false;
    }

    async init() {
        try {
            // Initialize database
            await db.init();
            console.log('Database initialized');

            // Initialize authentication
            await auth.init();
            console.log('Authentication initialized');

            // Check if user is already logged in
            if (auth.isLoggedIn()) {
                console.log('User already logged in:', auth.getCurrentUser());
                this.redirectToDashboard();
                return;
            } else {
                console.log('No user logged in, showing login form');
            }

            // Initialize UI
            this.initializeUI();
            this.isInitialized = true;
            
            console.log('Application initialized successfully');
        } catch (error) {
            console.error('Failed to initialize application:', error);
            Utils.showMessage('loginMessage', 'Failed to initialize application. Please refresh the page.', 'error');
        }
    }

    initializeUI() {
        // Login form handler
        const loginForm = document.getElementById('loginForm');
        if (loginForm) {
            loginForm.addEventListener('submit', this.handleLogin.bind(this));
        }

        // Support modal handlers
        this.initializeSupportModal();

        // Clear any existing messages
        Utils.clearMessage('loginMessage');
    }

    initializeSupportModal() {
        const supportBtn = document.getElementById('supportBtn');
        const supportModal = document.getElementById('supportModal');
        const closeBtn = supportModal?.querySelector('.close');

        if (supportBtn && supportModal) {
            supportBtn.addEventListener('click', () => {
                supportModal.style.display = 'block';
            });

            if (closeBtn) {
                closeBtn.addEventListener('click', () => {
                    supportModal.style.display = 'none';
                });
            }

            // Close modal when clicking outside
            window.addEventListener('click', (event) => {
                if (event.target === supportModal) {
                    supportModal.style.display = 'none';
                }
            });
        }
    }

    async handleLogin(event) {
        event.preventDefault();
        
        const formData = new FormData(event.target);
        const username = formData.get('username').trim();
        const password = formData.get('password');
        const userType = formData.get('userType');

        // Validate input
        if (!username || !password || !userType) {
            Utils.showMessage('loginMessage', 'Please fill in all fields', 'error');
            return;
        }

        // Show loading state
        const submitBtn = event.target.querySelector('button[type="submit"]');
        const originalText = submitBtn.textContent;
        submitBtn.textContent = 'Logging in...';
        submitBtn.disabled = true;

        try {
            console.log('Attempting login with:', { username, userType });
            const result = await auth.login(username, password, userType);
            console.log('Login result:', result);

            if (result.success) {
                Utils.showMessage('loginMessage', 'Login successful! Redirecting...', 'success');
                console.log('Login successful, current user:', auth.getCurrentUser());

                // Redirect after a short delay
                setTimeout(() => {
                    this.redirectToDashboard();
                }, 1000);
            } else {
                Utils.showMessage('loginMessage', result.message, 'error');
            }
        } catch (error) {
            console.error('Login error:', error);
            Utils.showMessage('loginMessage', 'Login failed. Please try again.', 'error');
        } finally {
            // Restore button state
            submitBtn.textContent = originalText;
            submitBtn.disabled = false;
        }
    }

    redirectToDashboard() {
        const user = auth.getCurrentUser();
        if (user) {
            // Use replace to avoid back button issues
            if (user.role === 'admin') {
                window.location.replace('admin.html');
            } else {
                window.location.replace('user.html');
            }
        }
    }

    // Service Worker registration for PWA
    async registerServiceWorker() {
        if ('serviceWorker' in navigator) {
            try {
                const registration = await navigator.serviceWorker.register('sw.js');
                console.log('Service Worker registered successfully:', registration);
            } catch (error) {
                console.log('Service Worker registration failed:', error);
            }
        }
    }
}

// Initialize application when DOM is loaded
document.addEventListener('DOMContentLoaded', async () => {
    const app = new App();
    await app.init();
    
    // Register service worker for PWA functionality
    await app.registerServiceWorker();
});

// Handle page visibility changes
document.addEventListener('visibilitychange', () => {
    if (!document.hidden && auth.isLoggedIn()) {
        // Refresh data when page becomes visible
        console.log('Page became visible, user is logged in');
    }
});

// Handle online/offline status
window.addEventListener('online', () => {
    console.log('Application is online');
});

window.addEventListener('offline', () => {
    console.log('Application is offline');
});

// Global error handler
window.addEventListener('error', (event) => {
    console.error('Global error:', event.error);
});

// Handle unhandled promise rejections
window.addEventListener('unhandledrejection', (event) => {
    console.error('Unhandled promise rejection:', event.reason);
    event.preventDefault();
});
