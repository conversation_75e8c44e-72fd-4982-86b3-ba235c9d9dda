<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Auth Test</title>
</head>
<body>
    <h1>Authentication Test</h1>
    <div id="status"></div>
    <button onclick="testLogin()">Test Admin Login</button>
    <button onclick="clearStorage()">Clear Storage</button>
    <button onclick="checkAuth()">Check Auth Status</button>

    <script src="js/database.js"></script>
    <script src="js/auth.js"></script>
    <script src="js/utils.js"></script>
    
    <script>
        async function init() {
            try {
                await db.init();
                await auth.init();
                console.log('Test page initialized');
                checkAuth();
            } catch (error) {
                console.error('Init error:', error);
            }
        }

        async function testLogin() {
            try {
                const result = await auth.login('admin', 'admin123', 'admin');
                console.log('Login result:', result);
                document.getElementById('status').innerHTML = `Login result: ${JSON.stringify(result, null, 2)}`;
                checkAuth();
            } catch (error) {
                console.error('Login error:', error);
                document.getElementById('status').innerHTML = `Login error: ${error.message}`;
            }
        }

        function clearStorage() {
            localStorage.clear();
            auth.currentUser = null;
            console.log('Storage cleared');
            document.getElementById('status').innerHTML = 'Storage cleared';
        }

        function checkAuth() {
            const user = auth.getCurrentUser();
            const isLoggedIn = auth.isLoggedIn();
            const isAdmin = auth.isAdmin();
            
            console.log('Auth status:', { user, isLoggedIn, isAdmin });
            document.getElementById('status').innerHTML = `
                <h3>Auth Status:</h3>
                <p>Current User: ${JSON.stringify(user, null, 2)}</p>
                <p>Is Logged In: ${isLoggedIn}</p>
                <p>Is Admin: ${isAdmin}</p>
                <p>LocalStorage: ${localStorage.getItem('currentUser')}</p>
            `;
        }

        // Initialize when page loads
        init();
    </script>
</body>
</html>
