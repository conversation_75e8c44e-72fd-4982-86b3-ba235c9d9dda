<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>User Dashboard - School Relief Time Table Creator</title>
    <link rel="stylesheet" href="css/styles.css">
    <link rel="stylesheet" href="css/admin.css">
    <link rel="manifest" href="manifest.json">
    <meta name="theme-color" content="#2196F3">
</head>
<body>
    <div class="admin-container">
        <header class="admin-header">
            <div class="header-content">
                <h1>User Dashboard</h1>
                <div class="user-info">
                    <span id="welcomeMessage">Welcome, User</span>
                    <button id="logoutBtn" class="btn btn-secondary">Logout</button>
                </div>
            </div>
        </header>

        <nav class="admin-nav">
            <ul class="nav-tabs">
                <li><a href="#dashboard" class="nav-link active" data-tab="dashboard">Dashboard</a></li>
                <li><a href="#timetables" class="nav-link" data-tab="timetables">View Timetables</a></li>
                <li><a href="#relief" class="nav-link" data-tab="relief">Relief Management</a></li>
                <li><a href="#history" class="nav-link" data-tab="history">Relief History</a></li>
            </ul>
        </nav>

        <main class="admin-main">
            <!-- Dashboard Tab -->
            <div id="dashboard" class="tab-content active">
                <div class="dashboard-grid">
                    <div class="dashboard-card">
                        <h3>Today's Summary</h3>
                        <div id="todaySummary">
                            <p>Date: <span id="currentDate"></span></p>
                            <p>Total Teachers: <span id="totalTeachers">0</span></p>
                            <p>Absent Teachers Today: <span id="absentTeachers">0</span></p>
                        </div>
                    </div>
                    <div class="dashboard-card">
                        <h3>Quick Actions</h3>
                        <div class="quick-actions">
                            <button class="btn btn-primary" onclick="userDashboard.switchTab('timetables')">View Timetables</button>
                            <button class="btn btn-success" onclick="userDashboard.switchTab('relief')">Create Relief</button>
                            <button class="btn btn-secondary" onclick="userDashboard.switchTab('history')">View History</button>
                        </div>
                    </div>
                    <div class="dashboard-card">
                        <h3>Today's Relief Status</h3>
                        <div id="todayReliefStatus">
                            <p>No relief timetable for today</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Timetables Tab -->
            <div id="timetables" class="tab-content">
                <div class="section-header">
                    <h2>Teacher Timetables</h2>
                    <div class="timetable-controls">
                        <select id="teacherSelect">
                            <option value="">Select Teacher</option>
                        </select>
                        <button id="viewTimetableBtn" class="btn btn-primary">View Timetable</button>
                        <button id="viewMasterTimetableBtn" class="btn btn-secondary">View Master Timetable</button>
                    </div>
                </div>
                <div id="timetableContainer">
                    <p class="no-data">Select a teacher to view their timetable</p>
                </div>
            </div>

            <!-- Relief Tab -->
            <div id="relief" class="tab-content">
                <div class="section-header">
                    <h2>Create Relief Timetable</h2>
                    <div class="relief-controls">
                        <input type="date" id="reliefDate" class="form-control">
                        <button id="createReliefBtn" class="btn btn-success">Create Relief</button>
                    </div>
                </div>
                <div id="reliefContainer">
                    <p class="no-data">Select a date and create a relief timetable</p>
                </div>
            </div>

            <!-- History Tab -->
            <div id="history" class="tab-content">
                <div class="section-header">
                    <h2>Relief Timetable History</h2>
                    <button id="refreshHistoryBtn" class="btn btn-secondary">Refresh</button>
                </div>
                <div id="historyContainer">
                    <!-- Relief history will be displayed here -->
                </div>
            </div>
        </main>

        <footer class="footer">
            <p>Design and developed by - Infotech Solutions - 0717754244</p>
            <button id="supportBtn" class="support-btn">Support</button>
        </footer>
    </div>

    <!-- Support Modal -->
    <div id="supportModal" class="modal">
        <div class="modal-content">
            <span class="close">&times;</span>
            <h3>Support Information</h3>
            <div class="support-info">
                <p><strong>Name:</strong> NAP Dharmapriya</p>
                <p><strong>Contact:</strong> 0717754244</p>
                <p><strong>Email:</strong> <EMAIL></p>
                <p><strong>Facebook:</strong> www.fb.com/pavidlk</p>
            </div>
        </div>
    </div>

    <!-- Dynamic Modals will be inserted here -->
    <div id="dynamicModals"></div>

    <script src="js/database.js"></script>
    <script src="js/auth.js"></script>
    <script src="js/utils.js"></script>
    <script src="js/timetable.js"></script>
    <script src="js/relief.js"></script>
    <script src="js/user.js"></script>
</body>
</html>
